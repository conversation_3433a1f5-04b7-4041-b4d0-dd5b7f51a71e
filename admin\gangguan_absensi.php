<?php
// Include file konfigurasi
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Proses persetujuan atau penolakan pengaduan
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $id = $_POST['id'] ?? 0;
    $action = $_POST['action'];
    $catatan = $_POST['catatan'] ?? '';

    if ($action == 'approve' || $action == 'reject') {
        $status = ($action == 'approve') ? 'approved' : 'rejected';

        // Update status pengaduan
        $query = "UPDATE gangguan_absensi SET status = '$status', catatan_admin = '$catatan' WHERE id = '$id'";
        if (mysqli_query($conn, $query)) {

            // Jika disetujui, tambahkan data presensi
            if ($status == 'approved') {
                // Ambil data pengaduan
                $query = "SELECT * FROM gangguan_absensi WHERE id = '$id'";
                $result = mysqli_query($conn, $query);
                $pengaduan = mysqli_fetch_assoc($result);

                if ($pengaduan) {
                    $user_id = $pengaduan['user_id'];
                    $tanggal = $pengaduan['tanggal'];
                    $jenis_absen = $pengaduan['jenis_absen'];
                    $bukti_foto = $pengaduan['bukti_foto'];

                    // Cek apakah sudah ada data presensi pada tanggal tersebut
                    $query = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$tanggal'";
                    $result = mysqli_query($conn, $query);

                    if (mysqli_num_rows($result) > 0) {
                        // Update data presensi yang sudah ada
                        $presensi = mysqli_fetch_assoc($result);

                        // Gunakan lokasi default tanpa query
                        $lokasi = 'Kantor';

                        // Gunakan bukti foto dari pengaduan jika ada, jika tidak gunakan default
                        $foto = !empty($bukti_foto) ? 'gangguan_absensi/' . $bukti_foto : 'default.jpg';

                        if ($jenis_absen == 'masuk') {
                            // Set jam masuk default (jam masuk normal)
                            $jam_kerja = getJamKerjaByUserId($user_id);
                            $jam_masuk = $jam_kerja ? $jam_kerja['jam_masuk'] : '08:00:00';

                            // Periksa apakah kolom keterangan sudah ada isinya
                            $keterangan_update = empty($presensi['keterangan']) ?
                                "'Gangguan absensi masuk'" :
                                "CONCAT(IFNULL(keterangan, ''), ' (Gangguan absensi masuk)')";

                            // Update dengan bukti foto dari pengaduan
                            $query = "UPDATE presensi SET
                                      jam_masuk = '$jam_masuk',
                                      foto_masuk = '$foto',
                                      lokasi_masuk = IFNULL(lokasi_masuk, '$lokasi'),
                                      status = 'Tepat Waktu',
                                      keterangan = $keterangan_update
                                      WHERE user_id = '$user_id' AND tanggal = '$tanggal'";
                            mysqli_query($conn, $query);
                        } elseif ($jenis_absen == 'pulang') {
                            // Set jam pulang default (jam pulang normal)
                            $jam_kerja = getJamKerjaByUserId($user_id);
                            $jam_pulang = $jam_kerja ? $jam_kerja['jam_pulang'] : '17:00:00';

                            // Periksa apakah kolom keterangan sudah ada isinya
                            $keterangan_update = empty($presensi['keterangan']) ?
                                "'Gangguan absensi pulang'" :
                                "CONCAT(IFNULL(keterangan, ''), ' (Gangguan absensi pulang)')";

                            // Update dengan bukti foto dari pengaduan
                            $query = "UPDATE presensi SET
                                      jam_pulang = '$jam_pulang',
                                      foto_pulang = '$foto',
                                      lokasi_pulang = '$lokasi',
                                      status = 'Tepat Waktu',
                                      keterangan = $keterangan_update
                                      WHERE user_id = '$user_id' AND tanggal = '$tanggal'";
                            mysqli_query($conn, $query);
                        }
                    } else {
                        // Insert data presensi baru
                        $jam_kerja = getJamKerjaByUserId($user_id);
                        $jam_masuk = $jam_kerja ? $jam_kerja['jam_masuk'] : '08:00:00';
                        $jam_pulang = $jam_kerja ? $jam_kerja['jam_pulang'] : '17:00:00';

                        // Gunakan lokasi default tanpa query
                        $lokasi = 'Kantor';

                        // Gunakan bukti foto dari pengaduan jika ada, jika tidak gunakan default
                        $foto = !empty($bukti_foto) ? 'gangguan_absensi/' . $bukti_foto : 'default.jpg';

                        if ($jenis_absen == 'masuk') {
                            // Untuk absen masuk, semua kolom wajib harus diisi
                            $query = "INSERT INTO presensi (user_id, tanggal, jam_masuk, foto_masuk, lokasi_masuk, jam_pulang, foto_pulang, lokasi_pulang, status, keterangan)
                                      VALUES ('$user_id', '$tanggal', '$jam_masuk', '$foto', '$lokasi', NULL, NULL, NULL, 'Tepat Waktu', 'Gangguan absensi masuk')";
                        } else {
                            // Cek apakah sudah ada data presensi pada tanggal tersebut
                            $check_query = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$tanggal'";
                            $check_result = mysqli_query($conn, $check_query);

                            if (mysqli_num_rows($check_result) > 0) {
                                // Update data yang sudah ada
                                $presensi_data = mysqli_fetch_assoc($check_result);

                                // Jika jam masuk kosong (tidak mungkin karena kolom NOT NULL), isi dengan nilai default
                                $update_jam_masuk = "";
                                $update_foto_masuk = "";
                                $update_lokasi_masuk = "";

                                if (empty($presensi_data['jam_masuk'])) {
                                    $update_jam_masuk = "jam_masuk = '$jam_masuk', ";
                                    $update_foto_masuk = "foto_masuk = '$foto', ";
                                    $update_lokasi_masuk = "lokasi_masuk = '$lokasi', ";
                                }

                                $query = "UPDATE presensi SET
                                          $update_jam_masuk
                                          $update_foto_masuk
                                          $update_lokasi_masuk
                                          jam_pulang = '$jam_pulang',
                                          foto_pulang = '$foto',
                                          lokasi_pulang = '$lokasi',
                                          status = 'Tepat Waktu',
                                          keterangan = CONCAT(IFNULL(keterangan, ''), ' (Gangguan absensi pulang)')
                                          WHERE user_id = '$user_id' AND tanggal = '$tanggal'";
                            } else {
                                // Insert baru dengan semua kolom wajib
                                $query = "INSERT INTO presensi (user_id, tanggal, jam_masuk, foto_masuk, lokasi_masuk, jam_pulang, foto_pulang, lokasi_pulang, status, keterangan)
                                          VALUES ('$user_id', '$tanggal', '$jam_masuk', '$foto', '$lokasi', '$jam_pulang', '$foto', '$lokasi', 'Tepat Waktu', 'Gangguan absensi masuk dan pulang')";
                            }
                        }

                        mysqli_query($conn, $query);
                    }
                }
            }

            setMessage('success', 'Pengaduan gangguan absensi berhasil diproses!');
        } else {
            setMessage('danger', 'Gagal memproses pengaduan gangguan absensi!');
        }
    }

    // Redirect untuk menghindari resubmission
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// Ambil data pengaduan gangguan absensi
$query = "SELECT ga.*, u.nama, u.nik FROM gangguan_absensi ga
          JOIN users u ON ga.user_id = u.id
          ORDER BY ga.status = 'pending' DESC, ga.created_at DESC";
$result = mysqli_query($conn, $query);
$pengaduan_list = [];

while ($row = mysqli_fetch_assoc($result)) {
    $pengaduan_list[] = $row;
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Pengaduan Gangguan Absensi</h1>
    </div>

    <?php
    // Tampilkan pesan jika ada
    $message = getMessage();
    if ($message): ?>
        <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show">
            <?php echo $message['text']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Daftar Pengaduan Gangguan Absensi</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Tanggal</th>
                            <th>NIK</th>
                            <th>Nama</th>
                            <th>Jenis Absen</th>
                            <th>Keterangan</th>
                            <th>Bukti Foto</th>
                            <th>Foto Wajah</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($pengaduan_list)): ?>
                            <tr>
                                <td colspan="10" class="text-center">Tidak ada data pengaduan gangguan absensi</td>
                            </tr>
                        <?php else: ?>
                            <?php $no = 1; foreach ($pengaduan_list as $pengaduan): ?>
                                <tr id="row-<?php echo $pengaduan['id']; ?>" class="<?php echo $pengaduan['status'] == 'pending' ? 'pending-row' : ''; ?>">
                                    <td><?php echo $no++; ?></td>
                                    <td><?php echo date('d/m/Y', strtotime($pengaduan['tanggal'])); ?></td>
                                    <td><?php echo $pengaduan['nik']; ?></td>
                                    <td><?php echo $pengaduan['nama']; ?></td>
                                    <td><?php echo ucfirst($pengaduan['jenis_absen']); ?></td>
                                    <td><?php echo $pengaduan['keterangan']; ?></td>
                                    <td>
                                        <?php if (!empty($pengaduan['bukti_foto'])): ?>
                                            <a href="<?php echo BASE_URL . 'uploads/gangguan_absensi/' . $pengaduan['bukti_foto']; ?>" target="_blank">
                                                <img src="<?php echo BASE_URL . 'uploads/gangguan_absensi/' . $pengaduan['bukti_foto']; ?>" alt="Bukti Foto" class="img-thumbnail" style="max-width: 100px;">
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">Tidak ada foto</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($pengaduan['foto_wajah'])): ?>
                                            <a href="<?php echo BASE_URL . 'uploads/gangguan_absensi/' . $pengaduan['foto_wajah']; ?>" target="_blank">
                                                <img src="<?php echo BASE_URL . 'uploads/gangguan_absensi/' . $pengaduan['foto_wajah']; ?>" alt="Foto Wajah" class="img-thumbnail" style="max-width: 100px;">
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">Tidak ada foto</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        $status_text = '';
                                        switch ($pengaduan['status']) {
                                            case 'pending':
                                                $status_class = 'warning';
                                                $status_text = 'Menunggu';
                                                break;
                                            case 'approved':
                                                $status_class = 'success';
                                                $status_text = 'Disetujui';
                                                break;
                                            case 'rejected':
                                                $status_class = 'danger';
                                                $status_text = 'Ditolak';
                                                break;
                                        }
                                        ?>
                                        <span class="badge bg-<?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                    </td>
                                    <td>
                                        <?php if ($pengaduan['status'] == 'pending'): ?>
                                            <a href="detail_gangguan_absensi.php?id=<?php echo $pengaduan['id']; ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> Detail
                                            </a>
                                        <?php else: ?>
                                            <a href="detail_gangguan_absensi.php?id=<?php echo $pengaduan['id']; ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> Detail
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Script untuk menangani form inline -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    // Kode untuk detail telah dipindahkan ke halaman terpisah

    // Fungsi untuk menangani tombol toggle approve
    $('.btn-toggle-approve').on('click', function() {
        var id = $(this).data('id');

        // Sembunyikan tombol aksi
        $(this).closest('.action-buttons').hide();

        // Tampilkan form approve
        $('.approve-form-' + id).show();

        // Fokus ke textarea
        $('#catatan_approve_' + id).focus();
    });

    // Fungsi untuk menangani tombol toggle reject
    $('.btn-toggle-reject').on('click', function() {
        var id = $(this).data('id');

        // Sembunyikan tombol aksi
        $(this).closest('.action-buttons').hide();

        // Tampilkan form reject
        $('.reject-form-' + id).show();

        // Fokus ke textarea
        $('#catatan_reject_' + id).focus();
    });

    // Fungsi untuk menangani tombol batal approve
    $('.btn-cancel-approve').on('click', function() {
        var id = $(this).data('id');

        // Sembunyikan form approve
        $('.approve-form-' + id).hide();

        // Reset form
        $('.approve-form-' + id + ' form')[0].reset();

        // Tampilkan tombol aksi
        $(this).closest('td').find('.action-buttons').show();
    });

    // Fungsi untuk menangani tombol batal reject
    $('.btn-cancel-reject').on('click', function() {
        var id = $(this).data('id');

        // Sembunyikan form reject
        $('.reject-form-' + id).hide();

        // Reset form
        $('.reject-form-' + id + ' form')[0].reset();

        // Tampilkan tombol aksi
        $(this).closest('td').find('.action-buttons').show();
    });

    // Fungsi untuk menangani form submission
    $('.inline-form').on('submit', function() {
        var submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true);
        submitBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Memproses...');

        // Biarkan form disubmit secara normal
        return true;
    });
});
</script>

<style>
/* CSS untuk modal detail telah dipindahkan ke halaman terpisah */

/* Styling untuk form inline */
.approval-form-container {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 10px;
    margin-top: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.inline-form .form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

.inline-form label {
    font-weight: 500;
    font-size: 0.85rem;
    color: #555;
}

/* Styling untuk baris pending */
.pending-row {
    background-color: #fff8e1 !important;
}

/* Styling untuk tombol aksi */
.action-buttons {
    display: flex;
    gap: 5px;
}

/* Animasi untuk form */
.approval-form-container {
    transition: all 0.3s ease;
}

/* Responsive styling */
@media (max-width: 768px) {
    .approval-form-container {
        padding: 8px;
    }

    .inline-form .form-control {
        font-size: 0.85rem;
    }

    .inline-form .btn {
        font-size: 0.85rem;
        padding: 0.25rem 0.5rem;
    }
}
</style>

<?php
// Include footer
include_once '../includes/footer.php';
?>
