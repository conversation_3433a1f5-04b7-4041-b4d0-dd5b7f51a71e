# Dokumentasi: <PERSON>tur Upload Foto pada Absensi Manual

## Deskripsi
Fitur ini memungkinkan admin untuk mengupload foto saat melakukan absensi manual untuk karyawan. Admin dapat mengupload foto terpisah untuk absen masuk dan absen pulang.

## Fitur yang Ditambahkan

### 1. Input Upload Foto
- **Foto Absen Masuk**: Input file untuk upload foto saat absen masuk
- **Foto Absen Pulang**: Input file untuk upload foto saat absen pulang
- **Preview Foto**: Menampilkan preview foto yang akan diupload
- **Validasi File**: Validasi format dan ukuran file

### 2. Validasi Upload
- **Format File**: JPG, PNG, JPEG
- **Ukuran Maksimal**: 2MB per file
- **Validasi Client-side**: JavaScript untuk validasi sebelum upload
- **Validasi Server-side**: PHP untuk validasi saat proses upload

### 3. Penyimpanan Foto
- **Direktori**: `uploads/manual_absensi/`
- **Nama File**: Format `{jenis}_{timestamp}_{uniqid}.{extension}`
- **Contoh**: `masuk_20231215_143022_abc123.jpg`

## Perubahan File

### 1. File: `admin/absensi_manual.php`

#### Perubahan Form HTML:
- Menambahkan `enctype="multipart/form-data"` pada form
- Menambahkan input file untuk foto masuk dan pulang
- Menambahkan div preview untuk menampilkan foto

#### Perubahan PHP:
- Menambahkan fungsi `uploadFoto()` untuk menangani upload
- Memodifikasi proses penyimpanan untuk menyertakan foto
- Menambahkan validasi upload foto

#### Perubahan JavaScript:
- Menambahkan event listener untuk preview foto
- Validasi ukuran dan format file di client-side

### 2. File: `admin/setup_manual_absensi.php` (Baru)
Script untuk setup awal:
- Membuat direktori `uploads/manual_absensi/`
- Membuat file gambar dummy `manual_absensi.jpg`

### 3. File: `docs/manual_absensi_with_photo.md` (Baru)
Dokumentasi lengkap fitur ini.

## Cara Penggunaan

### 1. Setup Awal
Jalankan script setup untuk membuat direktori dan file dummy:
```
http://localhost/absensiku/admin/setup_manual_absensi.php
```

### 2. Menggunakan Fitur Upload Foto
1. Buka halaman **Admin > Absensi Manual**
2. Isi form absensi seperti biasa
3. **Upload Foto Masuk** (opsional):
   - Klik "Choose File" pada bagian "Foto Absen Masuk"
   - Pilih file gambar (JPG/PNG, max 2MB)
   - Preview akan muncul otomatis
4. **Upload Foto Pulang** (opsional):
   - Klik "Choose File" pada bagian "Foto Absen Pulang"
   - Pilih file gambar (JPG/PNG, max 2MB)
   - Preview akan muncul otomatis
5. Pilih karyawan dan submit form

### 3. Hasil Penyimpanan
- Jika foto diupload: Foto akan disimpan di database dengan path file
- Jika tidak ada foto: Menggunakan file dummy `manual_absensi.jpg`
- Foto tersimpan di direktori `uploads/manual_absensi/`

## Struktur Database

### Tabel: `presensi`
- **foto_masuk**: Menyimpan path foto absen masuk
- **foto_pulang**: Menyimpan path foto absen pulang

Contoh data:
```sql
foto_masuk: 'uploads/manual_absensi/masuk_20231215_143022_abc123.jpg'
foto_pulang: 'uploads/manual_absensi/pulang_20231215_170022_def456.jpg'
```

## Keamanan

### 1. Validasi File
- Hanya menerima format gambar (JPG, PNG, JPEG)
- Membatasi ukuran file maksimal 2MB
- Validasi MIME type untuk mencegah upload file berbahaya

### 2. Nama File
- Menggunakan timestamp dan uniqid untuk mencegah konflik nama
- Tidak menggunakan nama file asli dari user

### 3. Direktori Upload
- Direktori terpisah untuk foto absensi manual
- Permissions yang sesuai (0777)

## Troubleshooting

### 1. Error "Direktori tidak ditemukan"
- Jalankan `admin/setup_manual_absensi.php`
- Atau buat direktori manual: `uploads/manual_absensi/`

### 2. Error "File terlalu besar"
- Pastikan file gambar kurang dari 2MB
- Kompres gambar jika perlu

### 3. Error "Format file tidak didukung"
- Gunakan format JPG, PNG, atau JPEG
- Pastikan file benar-benar gambar

### 4. Preview tidak muncul
- Pastikan JavaScript aktif di browser
- Cek console browser untuk error

## Maintenance

### 1. Pembersihan File
Buat script untuk membersihkan file foto lama yang tidak terpakai:
```php
// Hapus file foto yang lebih dari 6 bulan
$old_files = glob('../uploads/manual_absensi/*');
foreach ($old_files as $file) {
    if (filemtime($file) < strtotime('-6 months')) {
        unlink($file);
    }
}
```

### 2. Backup
- Sertakan direktori `uploads/manual_absensi/` dalam backup rutin
- Backup database untuk menyimpan referensi path foto

## Pengembangan Selanjutnya

### 1. Fitur Tambahan
- Crop/resize foto otomatis
- Watermark pada foto
- Kompresi foto untuk menghemat storage

### 2. Integrasi
- Tampilkan foto di halaman monitoring absensi
- Export foto dalam laporan absensi
- Galeri foto absensi karyawan
