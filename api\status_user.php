<?php
// <PERSON><PERSON><PERSON><PERSON> error untuk debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Koneksi & config
require_once '../config/database.php';
require_once '../config/config.php';

// Header & CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Validasi API key
function validateApiKey($api_key) {
    return $api_key === 'absensiku_api_key_2023';
}

// Ambil method & input
$method = $_SERVER['REQUEST_METHOD'];
$input = file_get_contents('php://input');
$data = json_decode($input, true);
if (!$data) $data = $_POST;

// API key dari GET jika tidak ada di body
if ($method === 'GET') {
    if (!isset($data['api_key']) && isset($_GET['api_key'])) {
        $data['api_key'] = $_GET['api_key'];
    }
}

// Validasi API key
if (!isset($data['api_key']) || !validateApiKey($data['api_key'])) {
    echo json_encode(['status' => 'error', 'message' => 'API key tidak valid']);
    exit;
}

// ========== GET ==========
if ($method === 'GET') {
    $where = [];
    if (isset($_GET['id'])) {
        $where[] = "id = '" . mysqli_real_escape_string($conn, $_GET['id']) . "'";
    }
    if (isset($_GET['nik'])) {
        $where[] = "nik = '" . mysqli_real_escape_string($conn, $_GET['nik']) . "'";
    }

    $sql = "SELECT id, nik, status_login, status_akun, device_id FROM status_user";
    if ($where) {
        $sql .= " WHERE " . implode(' AND ', $where);
    }
    $sql .= " ORDER BY id ASC";

    $result = mysqli_query($conn, $sql);
    $rows = [];

    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $rows[] = $row;
        }
    }

    echo json_encode(['status' => 'success', 'data' => $rows]);
    exit;
}

// ========== POST ==========
if ($method === 'POST') {
    $nik = mysqli_real_escape_string($conn, $data['nik'] ?? '');
    $status_login = mysqli_real_escape_string($conn, $data['status_login'] ?? '');
    $status_akun = mysqli_real_escape_string($conn, $data['status_akun'] ?? '');
    $device_id = mysqli_real_escape_string($conn, $data['device_id'] ?? '');

    if (!in_array($status_login, ['login', 'tidak']) || !in_array($status_akun, ['aktif', 'blokir'])) {
        echo json_encode(['status' => 'error', 'message' => 'Status login/akun tidak valid']);
        exit;
    }

    $sql = "INSERT INTO status_user (nik, status_login, status_akun, device_id) VALUES ('$nik', '$status_login', '$status_akun', '$device_id')";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Data berhasil ditambahkan']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal menambah data: ' . mysqli_error($conn)]);
    }
    exit;
}

// ========== PUT ==========
if ($method === 'PUT') {
    parse_str(file_get_contents("php://input"), $put_vars);

    $id = mysqli_real_escape_string($conn, $data['id'] ?? $put_vars['id'] ?? '');
    if (!$id) {
        echo json_encode(['status' => 'error', 'message' => 'ID wajib diisi']);
        exit;
    }

    $fields = [];
    foreach (['nik', 'status_login', 'status_akun', 'device_id'] as $col) {
        $val = $data[$col] ?? $put_vars[$col] ?? null;
        if (!is_null($val)) {
            if ($col === 'status_login' && !in_array($val, ['login', 'tidak'])) continue;
            if ($col === 'status_akun' && !in_array($val, ['aktif', 'blokir'])) continue;

            $fields[] = "$col = '" . mysqli_real_escape_string($conn, $val) . "'";
        }
    }

    if (empty($fields)) {
        echo json_encode(['status' => 'error', 'message' => 'Tidak ada data untuk diperbarui']);
        exit;
    }

    $sql = "UPDATE status_user SET " . implode(', ', $fields) . " WHERE id = '$id'";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Data berhasil diupdate']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal update data: ' . mysqli_error($conn)]);
    }
    exit;
}

// ========== DELETE ==========
if ($method === 'DELETE') {
    parse_str(file_get_contents("php://input"), $del_vars);
    $id = mysqli_real_escape_string($conn, $data['id'] ?? $del_vars['id'] ?? '');

    if (!$id) {
        echo json_encode(['status' => 'error', 'message' => 'ID wajib diisi']);
        exit;
    }

    $sql = "DELETE FROM status_user WHERE id = '$id'";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Data berhasil dihapus']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal menghapus data: ' . mysqli_error($conn)]);
    }
    exit;
}

// ========== DEFAULT ==========
echo json_encode(['status' => 'error', 'message' => 'Metode tidak didukung']);
?>
