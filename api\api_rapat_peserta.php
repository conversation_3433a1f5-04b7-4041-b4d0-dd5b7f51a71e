<?php
// <PERSON><PERSON><PERSON><PERSON> error untuk debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Koneksi & config
require_once '../config/database.php';
require_once '../config/config.php';

// Header & CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Validasi API key
function validateApiKey($api_key) {
    return $api_key === 'absensiku_api_key_2023';
}

// Ambil method & input
$method = $_SERVER['REQUEST_METHOD'];
$input = file_get_contents('php://input');
$data = json_decode($input, true);
if (!$data) $data = $_POST;

// Ambil API key dari GET jika tidak ada di body
if ($method === 'GET') {
    if (!isset($data['api_key']) && isset($_GET['api_key'])) {
        $data['api_key'] = $_GET['api_key'];
    }
}

// Validasi API key
if (!isset($data['api_key']) || !validateApiKey($data['api_key'])) {
    echo json_encode(['status' => 'error', 'message' => 'API key tidak valid']);
    exit;
}

// ========== GET ==========
if ($method === 'GET') {
    $sql = "SELECT * FROM rapat_peserta ORDER BY created_at DESC";
    $result = mysqli_query($conn, $sql);
    $rows = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $rows[] = $row;
    }
    echo json_encode(['status' => 'success', 'data' => $rows]);
    exit;
}

// ========== POST ==========
if ($method === 'POST') {
    $rapat_id = mysqli_real_escape_string($conn, $data['rapat_id'] ?? '');
    $user_id = mysqli_real_escape_string($conn, $data['user_id'] ?? '');
    $status = mysqli_real_escape_string($conn, $data['status'] ?? '');
    $waktu_hadir = mysqli_real_escape_string($conn, $data['waktu_hadir'] ?? '');
    $created_at = date('Y-m-d H:i:s');

    if (!$rapat_id || !$user_id) {
        echo json_encode(['status' => 'error', 'message' => 'Rapat ID dan User ID wajib diisi']);
        exit;
    }

    // Cek apakah kombinasi sudah ada
    $cek = mysqli_query($conn, "SELECT id FROM rapat_peserta WHERE rapat_id='$rapat_id' AND user_id='$user_id'");
    if (mysqli_num_rows($cek) > 0) {
        echo json_encode([
            'status' => 'duplicate',
            'message' => 'Peserta sudah terdaftar dalam rapat ini'
        ]);
        exit;
    }

    // Insert baru
    $sql = "INSERT INTO rapat_peserta (rapat_id, user_id, status, waktu_hadir, created_at)
            VALUES ('$rapat_id', '$user_id', '$status', '$waktu_hadir', '$created_at')";

    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Peserta berhasil ditambahkan']);
    } else {
        echo json_encode(['status' => 'error', 'message' => mysqli_error($conn)]);
    }
    exit;
}

// ========== PUT ==========
if ($method === 'PUT') {
    $rapat_id = mysqli_real_escape_string($conn, $data['rapat_id'] ?? '');
    $user_id = mysqli_real_escape_string($conn, $data['user_id'] ?? '');
    $status = mysqli_real_escape_string($conn, $data['status'] ?? '');
    $waktu_hadir = mysqli_real_escape_string($conn, $data['waktu_hadir'] ?? '');
    $updated_at = date('Y-m-d H:i:s');

    if (!$rapat_id || !$user_id) {
        echo json_encode(['status' => 'error', 'message' => 'Rapat ID dan User ID wajib diisi untuk update']);
        exit;
    }

    $sql = "UPDATE rapat_peserta 
            SET status='$status', waktu_hadir='$waktu_hadir', updated_at='$updated_at'
            WHERE rapat_id='$rapat_id' AND user_id='$user_id'";

    if (mysqli_query($conn, $sql)) {
        if (mysqli_affected_rows($conn) > 0) {
            echo json_encode(['status' => 'success', 'message' => 'Data berhasil diperbarui']);
        } else {
            echo json_encode(['status' => 'warning', 'message' => 'Tidak ada data yang diperbarui (data tidak ditemukan atau tidak berubah)']);
        }
    } else {
        echo json_encode(['status' => 'error', 'message' => mysqli_error($conn)]);
    }
    exit;
}
?>
