<?php
// Include file konfigurasi
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/functions.php';

// Cek akses
checkAccess('admin');

// Buat direktori untuk foto absensi manual jika belum ada
$upload_dir = '../uploads/manual_absensi/';
if (!file_exists($upload_dir)) {
    if (mkdir($upload_dir, 0777, true)) {
        setMessage('success', 'Direktori uploads/manual_absensi/ berhasil dibuat!');
    } else {
        setMessage('danger', 'Gagal membuat direktori uploads/manual_absensi/');
    }
}

// Buat file gambar dummy jika belum ada
$dummy_file = '../uploads/manual_absensi.jpg';
if (!file_exists($dummy_file)) {
    // Buat gambar dummy sederhana
    $width = 300;
    $height = 200;
    $image = imagecreate($width, $height);
    
    // Warna
    $bg_color = imagecolorallocate($image, 240, 240, 240);
    $text_color = imagecolorallocate($image, 100, 100, 100);
    
    // Teks
    $text = 'ABSENSI MANUAL';
    $font_size = 3;
    $text_width = imagefontwidth($font_size) * strlen($text);
    $text_height = imagefontheight($font_size);
    $x = ($width - $text_width) / 2;
    $y = ($height - $text_height) / 2;
    
    imagestring($image, $font_size, $x, $y, $text, $text_color);
    
    // Simpan gambar
    if (imagejpeg($image, $dummy_file)) {
        setMessage('success', 'File gambar dummy manual_absensi.jpg berhasil dibuat!');
    } else {
        setMessage('danger', 'Gagal membuat file gambar dummy');
    }
    
    imagedestroy($image);
} else {
    setMessage('info', 'File gambar dummy sudah ada');
}

// Redirect ke halaman absensi manual
redirect('admin/absensi_manual.php');
?>
