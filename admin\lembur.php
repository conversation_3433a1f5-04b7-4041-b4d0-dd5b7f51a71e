<?php
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

checkAccess('admin');

// Ambil semua data lembur
$query = "SELECT * FROM lembur ORDER BY tanggal_lembur DESC";
$result = mysqli_query($conn, $query);

// Ambil nama karyawan unik
$karyawan_result = mysqli_query($conn, "SELECT DISTINCT nama_karyawan FROM lembur ORDER BY nama_karyawan ASC");

// Ambil periode unik
$periode_result = mysqli_query($conn, "
    SELECT DISTINCT DATE_FORMAT(tanggal_lembur, '%Y-%m') AS periode
    FROM lembur
    ORDER BY periode DESC
");

function formatPeriode($ym) {
    $bulanMap = [
        '01'=>'Januari','02'=>'Februari','03'=>'Maret','04'=>'April','05'=>'Mei','06'=>'Juni',
        '07'=>'Juli','08'=>'Agustus','09'=>'September','10'=>'Oktober','11'=>'November','12'=>'Desember'
    ];
    [$tahun, $bulan] = explode('-', $ym);
    return $bulanMap[$bulan] . ' ' . $tahun;
}

function hitungDurasi($mulai, $selesai) {
    if (!$mulai || !$selesai) return '-';
    $start = new DateTime($mulai);
    $end = new DateTime($selesai);
    $interval = $start->diff($end);
    return $interval->format('%h jam %i menit');
}

include_once '../includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Data Lembur</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item active">Lembur</li>
    </ol>
<div class="mb-3">
    <a href="export_lembur.php" class="btn btn-success">
        <i class="fas fa-file-excel"></i> Export ke Excel
    </a>
</div>
    <!-- Filter -->
    <div class="row mb-3">
        <div class="col-md-6">
            <label class="form-label">Filter Nama Karyawan:</label>
            <select class="form-select" id="filterNama">
                <option value="">-- Semua Nama --</option>
                <?php while ($row = mysqli_fetch_assoc($karyawan_result)) {
                    echo '<option value="' . htmlspecialchars($row['nama_karyawan']) . '">' . htmlspecialchars($row['nama_karyawan']) . '</option>';
                } ?>
            </select>
        </div>
        <div class="col-md-6">
            <label class="form-label">Filter Periode Bulan:</label>
            <select class="form-select" id="filterPeriode">
                <option value="">-- Semua Periode --</option>
                <?php while ($row = mysqli_fetch_assoc($periode_result)) {
                    $periode = $row['periode'];
                    echo '<option value="' . $periode . '">' . formatPeriode($periode) . '</option>';
                } ?>
            </select>
        </div>
    </div>

    <!-- Tabel -->
    <div class="card mb-4">
        <div class="card-header"><i class="fas fa-clock me-1"></i> Daftar Lembur</div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="lemburTable" class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Nama Karyawan</th>
                            <th>Tanggal</th>
                            <th>Jam Mulai</th>
                            <th>Jam Selesai</th>
                            <th>Durasi</th>
                            <th>Keterangan</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $no = 1;
                        mysqli_data_seek($result, 0);
                        while ($row = mysqli_fetch_assoc($result)) {
                            $periodeValue = date('Y-m', strtotime($row['tanggal_lembur']));
                            echo '<tr data-nama="' . htmlspecialchars($row['nama_karyawan']) . '" data-periode="' . $periodeValue . '">
                                <td>' . $no++ . '</td>
                                <td>' . htmlspecialchars($row['nama_karyawan']) . '</td>
                                <td>' . date('d/m/Y', strtotime($row['tanggal_lembur'])) . '</td>
                                <td>' . htmlspecialchars($row['jam_mulai']) . '</td>
                                <td>' . htmlspecialchars($row['jam_selesai']) . '</td>
                                <td>' . hitungDurasi($row['jam_mulai'], $row['jam_selesai']) . '</td>
                                <td>' . htmlspecialchars($row['keterangan']) . '</td>
                                <td>' . htmlspecialchars($row['status']) . '</td>
                            </tr>';
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>

<script>
$(document).ready(function () {
    const table = $('#lemburTable').DataTable();

    function filterTabel() {
        const nama = $('#filterNama').val();
        const periode = $('#filterPeriode').val();

        table.rows().every(function () {
            const row = $(this.node());
            const rowNama = row.data('nama');
            const rowPeriode = row.data('periode');
            const matchNama = !nama || rowNama === nama;
            const matchPeriode = !periode || rowPeriode === periode;

            if (matchNama && matchPeriode) {
                row.show();
            } else {
                row.hide();
            }
        });
    }

    $('#filterNama, #filterPeriode').on('change', filterTabel);
});
</script>
