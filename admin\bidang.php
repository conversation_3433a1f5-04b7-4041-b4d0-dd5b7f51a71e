<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Proses tambah bidang
if (isset($_POST['tambah'])) {
    $nama_bidang = clean($_POST['nama_bidang']);
    $keterangan = clean($_POST['keterangan']);

    // Cek apakah nama bidang sudah terdaftar
    $query = "SELECT * FROM bidang WHERE nama_bidang = '$nama_bidang'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        setMessage('danger', 'Nama bidang sudah terdaftar!');
        redirect('admin/bidang.php');
    }

    // Insert data bidang baru
    $query = "INSERT INTO bidang (nama_bidang, keterangan) VALUES ('$nama_bidang', '$keterangan')";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Bidang berhasil ditambahkan!');
    } else {
        setMessage('danger', 'Gagal menambahkan bidang!');
    }

    redirect('admin/bidang.php');
}

// Proses edit bidang
if (isset($_POST['edit'])) {
    $id = clean($_POST['id']);
    $nama_bidang = clean($_POST['nama_bidang']);
    $keterangan = clean($_POST['keterangan']);

    // Cek apakah nama bidang sudah terdaftar (selain bidang ini)
    $query = "SELECT * FROM bidang WHERE nama_bidang = '$nama_bidang' AND id != '$id'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        setMessage('danger', 'Nama bidang sudah terdaftar!');
        redirect('admin/bidang.php');
    }

    // Update data bidang
    $query = "UPDATE bidang SET nama_bidang = '$nama_bidang', keterangan = '$keterangan' WHERE id = '$id'";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Data bidang berhasil diperbarui!');
    } else {
        setMessage('danger', 'Gagal memperbarui data bidang!');
    }

    redirect('admin/bidang.php');
}

// Proses hapus bidang
if (isset($_GET['hapus'])) {
    $id = clean($_GET['hapus']);

    // Cek apakah bidang digunakan oleh karyawan
    $query = "SELECT * FROM users WHERE bidang_id = '$id'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        setMessage('danger', 'Bidang tidak dapat dihapus karena digunakan oleh karyawan!');
        redirect('admin/bidang.php');
    }

    // Cek apakah bidang digunakan oleh jam kerja
    $query = "SELECT * FROM jam_kerja WHERE bidang_id = '$id'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        setMessage('danger', 'Bidang tidak dapat dihapus karena digunakan oleh jam kerja!');
        redirect('admin/bidang.php');
    }

    // Hapus bidang
    $query = "DELETE FROM bidang WHERE id = '$id'";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Bidang berhasil dihapus!');
    } else {
        setMessage('danger', 'Gagal menghapus bidang!');
    }

    redirect('admin/bidang.php');
}

// Ambil data bidang
$query = "SELECT * FROM bidang ORDER BY nama_bidang ASC";
$result = mysqli_query($conn, $query);

$bidang = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $bidang[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Manajemen Bidang</h1>
        <div>
            <!--<a href="update_database.php" class="btn btn-warning me-2">-->
            <!--    <i class="fas fa-database"></i> Update Database-->
            <!--</a>-->
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#tambahBidangModal">
                <i class="fas fa-plus"></i> Tambah Bidang
            </button>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold">Data Bidang</h6>
            <a href="jam_kerja.php" class="btn btn-sm btn-info">
                <i class="fas fa-clock"></i> Kelola Jam Kerja
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Nama Bidang</th>
                            <th>Keterangan</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($bidang)): ?>
                            <tr>
                                <td colspan="3" class="text-center">Tidak ada data bidang</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($bidang as $b): ?>
                                <tr>
                                    <td><?php echo $b['nama_bidang']; ?></td>
                                    <td><?php echo $b['keterangan']; ?></td>
                                    <td>
                                        <a href="edit_bidang.php?id=<?php echo $b['id']; ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <a href="bidang.php?hapus=<?php echo $b['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus bidang ini?')">
                                            <i class="fas fa-trash"></i> Hapus
                                        </a>
                                    </td>
                                </tr>


                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Informasi Bidang</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h5 class="alert-heading">Penggunaan Bidang:</h5>
                <ul>
                    <li>Bidang digunakan untuk mengelompokkan karyawan berdasarkan departemen atau divisi.</li>
                    <li>Setiap bidang dapat memiliki jam kerja yang berbeda.</li>
                    <li>Bidang juga dapat digunakan untuk menentukan hari kerja dan hari libur khusus.</li>
                </ul>
                <hr>
                <p class="mb-0">Pastikan untuk membuat bidang terlebih dahulu sebelum menambahkan karyawan atau mengatur jam kerja.</p>
            </div>

            <div class="d-flex justify-content-between mt-3">
                <a href="jam_kerja.php" class="btn btn-primary">
                    <i class="fas fa-clock"></i> Kelola Jam Kerja
                </a>
                <a href="hari_kerja.php" class="btn btn-success">
                    <i class="fas fa-calendar-day"></i> Kelola Hari Kerja
                </a>
                <a href="jam_kerja_bidang.php" class="btn btn-info">
                    <i class="fas fa-calendar-alt"></i> Kelola Jam Kerja Bidang
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Bidang -->
<div class="modal fade" id="tambahBidangModal" tabindex="-1" aria-labelledby="tambahBidangModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tambahBidangModalLabel">Tambah Bidang</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="nama_bidang" class="form-label">Nama Bidang</label>
                        <input type="text" class="form-control" id="nama_bidang" name="nama_bidang" required>
                    </div>

                    <div class="mb-3">
                        <label for="keterangan" class="form-label">Keterangan</label>
                        <textarea class="form-control" id="keterangan" name="keterangan" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" name="tambah" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
