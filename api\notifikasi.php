<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../config/config.php';

// Cek method request
$method = $_SERVER['REQUEST_METHOD'];

switch($method) {
    case 'GET':
        $response = [];
        
        // Query untuk izin dinas yang pending
        $sql_izin = "SELECT i.*, u.nama as nama_karyawan, u.nik, u.bidang_id, b.nama_bidang
                    FROM izin_dinas i 
                    JOIN users u ON i.user_id = u.id 
                    LEFT JOIN bidang b ON u.bidang_id = b.id
                    WHERE i.status = 'pending' AND i.is_notified = 0
                    ORDER BY i.created_at DESC";
        
        $result_izin = mysqli_query($conn, $sql_izin);
        if (!$result_izin) {
            echo json_encode([
                'status' => 'error',
                'message' => 'Error query izin: ' . mysqli_error($conn)
            ]);
            exit;
        }
        
        $data_izin = [];
        while ($row = mysqli_fetch_assoc($result_izin)) {
            $data_izin[] = [
                'id' => $row['id'],
                'nik' => $row['nik'],
                'nama' => $row['nama_karyawan'],
                'bidang' => $row['nama_bidang'],
                'tanggal_mulai' => $row['tanggal_mulai'],
                'tanggal_selesai' => $row['tanggal_selesai'],
                'tujuan' => $row['tujuan'],
                'keterangan' => $row['keterangan'],
                'foto_surat_tugas' => $row['foto_surat_tugas'],
                'foto_wajah' => $row['foto_wajah'],
                'status' => $row['status'],
                'created_at' => $row['created_at']
            ];
        }
        
        $response['izin'] = $data_izin;
        
        // Query untuk gangguan absen yang pending
        $sql_gangguan = "SELECT g.*, u.nama as nama_karyawan, u.nik, u.bidang_id, b.nama_bidang
                        FROM gangguan_absensi g 
                        JOIN users u ON g.user_id = u.id 
                        LEFT JOIN bidang b ON u.bidang_id = b.id
                        WHERE g.status = 'pending' AND g.is_notified = 0
                        ORDER BY g.created_at DESC";
        
        $result_gangguan = mysqli_query($conn, $sql_gangguan);
        if (!$result_gangguan) {
            echo json_encode([
                'status' => 'error',
                'message' => 'Error query gangguan: ' . mysqli_error($conn)
            ]);
            exit;
        }
        
        $data_gangguan = [];
        while ($row = mysqli_fetch_assoc($result_gangguan)) {
            $data_gangguan[] = [
                'id' => $row['id'],
                'nik' => $row['nik'],
                'nama' => $row['nama_karyawan'],
                'bidang' => $row['nama_bidang'],
                'tanggal' => $row['tanggal'],
                'jenis_absen' => $row['jenis_absen'],
                'keterangan' => $row['keterangan'],
                'bukti_foto' => $row['bukti_foto'],
                'foto_wajah' => $row['foto_wajah'],
                'status' => $row['status'],
                'created_at' => $row['created_at']
            ];
        }
        
        $response['gangguan'] = $data_gangguan;
        
        // Update status is_notified untuk data yang sudah diambil
        if (!empty($data_izin)) {
            $izin_ids = array_column($data_izin, 'id');
            $izin_ids_str = implode(',', $izin_ids);
            mysqli_query($conn, "UPDATE izin_dinas SET is_notified = 1 WHERE id IN ($izin_ids_str)");
        }
        
        if (!empty($data_gangguan)) {
            $gangguan_ids = array_column($data_gangguan, 'id');
            $gangguan_ids_str = implode(',', $gangguan_ids);
            mysqli_query($conn, "UPDATE gangguan_absensi SET is_notified = 1 WHERE id IN ($gangguan_ids_str)");
        }
        
        echo json_encode([
            'status' => 'success',
            'data' => $response
        ]);
        break;
        
    case 'POST':
        // Ambil data dari body request
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            echo json_encode([
                'status' => 'error',
                'message' => 'Data tidak valid'
            ]);
            exit;
        }
        
        $jenis = isset($data['jenis']) ? clean($data['jenis']) : null;
        $user_id = isset($data['user_id']) ? clean($data['user_id']) : null;
        
        if (!$jenis || !$user_id) {
            echo json_encode([
                'status' => 'error',
                'message' => 'Jenis dan User ID diperlukan'
            ]);
            exit;
        }
        
        // Proses berdasarkan jenis
        if ($jenis == 'izin') {
            $tanggal_mulai = clean($data['tanggal_mulai']);
            $tanggal_selesai = clean($data['tanggal_selesai']);
            $tujuan = clean($data['tujuan']);
            $keterangan = clean($data['keterangan']);
            $foto_surat_tugas = clean($data['foto_surat_tugas']);
            $foto_wajah = clean($data['foto_wajah']);
            
            $sql = "INSERT INTO izin_dinas (user_id, tanggal_mulai, tanggal_selesai, tujuan, keterangan, 
                    foto_surat_tugas, foto_wajah, status, is_notified) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', 0)";
            
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("issssss", $user_id, $tanggal_mulai, $tanggal_selesai, $tujuan, 
                            $keterangan, $foto_surat_tugas, $foto_wajah);
            
        } elseif ($jenis == 'gangguan') {
            $tanggal = clean($data['tanggal']);
            $jenis_absen = clean($data['jenis_absen']);
            $keterangan = clean($data['keterangan']);
            $bukti_foto = clean($data['bukti_foto']);
            $foto_wajah = clean($data['foto_wajah']);
            
            $sql = "INSERT INTO gangguan_absensi (user_id, tanggal, jenis_absen, keterangan, 
                    bukti_foto, foto_wajah, status, is_notified) 
                    VALUES (?, ?, ?, ?, ?, ?, 'pending', 0)";
            
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("isssss", $user_id, $tanggal, $jenis_absen, $keterangan, 
                            $bukti_foto, $foto_wajah);
        }
        
        if ($stmt->execute()) {
            echo json_encode([
                'status' => 'success',
                'message' => 'Data berhasil disimpan',
                'id' => $conn->insert_id
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => 'Gagal menyimpan data: ' . $conn->error
            ]);
        }
        break;
        
    default:
        echo json_encode([
            'status' => 'error',
            'message' => 'Method tidak diizinkan'
        ]);
        break;
}
?> 