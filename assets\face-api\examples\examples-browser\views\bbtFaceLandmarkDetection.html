<!DOCTYPE html>
<html>
<head>
  <script src="face-api.js"></script>
  <script src="js/commons.js"></script>
  <script src="js/bbt.js"></script>
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/0.100.2/css/materialize.css">
  <script type="text/javascript" src="https://code.jquery.com/jquery-2.1.1.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/0.100.2/js/materialize.min.js"></script>
</head>
<body>
  <div id="navbar"></div>
  <div class="center-content page-container">
    <div>
      <div class="progress" id="loader">
        <div class="indeterminate"></div>
      </div>
      <div class="row side-by-side">
        <div class="center-content">
          <div id="faceContainer"></div>
          <div id="selectList"></div>
        </div>
      </div>
      <p>
        <input type="checkbox" id="drawLinesCheckbox" checked="checked" onchange="onChangeDrawLines(event)" />
        <label for="drawLinesCheckbox">Draw Lines</label>
      </p>
    </div>
  </div>

  <script>
    let drawLines = true
    let landmarks
    let currentImg

    function onChangeDrawLines(e) {
      drawLines = $(e.target).prop('checked')
      redraw()
    }

    function redraw() {
      const canvas = faceapi.createCanvasFromMedia(currentImg)
      $('#faceContainer').empty()
      $('#faceContainer').append(canvas)
      new faceapi.draw.DrawFaceLandmarks(landmarks, { drawLines }).draw(canvas)
    }

    async function onSelectionChanged(uri) {
      currentImg = await faceapi.fetchImage(uri)
      landmarks = await faceapi.detectFaceLandmarks(currentImg)
      redraw()
    }

    async function run() {
      await faceapi.loadFaceLandmarkModel('/')
      $('#loader').hide()
      await onSelectionChanged($('#selectList select').val())
    }

    $(document).ready(function() {
      renderNavBar('#navbar', 'bbt_face_landmark_detection')
      renderFaceImageSelectList(
        '#selectList',
        onSelectionChanged,
        { className: 'sheldon', imageIdx: 1 }
      )
      run()
    })

  </script>

</body>
</html>