<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Cek akses
checkAccess('admin');

// Proses tambah karyawan
if (isset($_POST['tambah'])) {
    $nik = clean($_POST['nik']);
    $nama = clean($_POST['nama']);
    $bidang_id = clean($_POST['bidang_id']);
    $jabatan = clean($_POST['jabatan']);
    $lokasi_id = clean($_POST['lokasi_id']);
    $password = clean($_POST['password']);

    // Validasi bidang_id
    if (empty($bidang_id)) {
        setMessage('danger', 'Bidang harus dipilih!');
        redirect('admin/karyawan.php');
    }

    // Cek apakah bidang_id valid
    $query = "SELECT * FROM bidang WHERE id = '$bidang_id'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) == 0) {
        setMessage('danger', 'Bidang yang dipilih tidak valid!');
        redirect('admin/karyawan.php');
    }

    // Upload foto profil
    $foto_profil = null;
    if (isset($_FILES['foto_profil']) && $_FILES['foto_profil']['error'] == 0) {
        $file_tmp = $_FILES['foto_profil']['tmp_name'];
        $file_name = $_FILES['foto_profil']['name'];
        $file_size = $_FILES['foto_profil']['size'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

        if (in_array($file_ext, ALLOWED_EXTENSIONS)) {
            if ($file_size <= MAX_UPLOAD_SIZE) {
                $foto_profil = 'profile_' . time() . '.' . $file_ext;
                $upload_path = UPLOAD_PATH . $foto_profil;

                if (!move_uploaded_file($file_tmp, $upload_path)) {
                    setMessage('danger', 'Gagal mengupload foto profil!');
                    redirect('admin/karyawan.php');
                }
            } else {
                setMessage('danger', 'Ukuran file terlalu besar! Maksimal ' . (MAX_UPLOAD_SIZE / 1024 / 1024) . 'MB');
                redirect('admin/karyawan.php');
            }
        } else {
            setMessage('danger', 'Ekstensi file tidak diizinkan! Hanya ' . implode(', ', ALLOWED_EXTENSIONS) . ' yang diizinkan');
            redirect('admin/karyawan.php');
        }
    }

    // Daftarkan karyawan
    if (registerKaryawan($nik, $nama, $bidang_id, $jabatan, $lokasi_id, $password, $foto_profil)) {
        setMessage('success', 'Karyawan berhasil ditambahkan!');
    } else {
        setMessage('danger', 'Gagal menambahkan karyawan! NIK mungkin sudah terdaftar atau bidang tidak valid.');
    }

    redirect('admin/karyawan.php');
}

// Proses hapus karyawan
if (isset($_GET['hapus'])) {
    $id = clean($_GET['hapus']);

    // Ambil data karyawan untuk konfirmasi
    $query = "SELECT * FROM users WHERE id = '$id' AND role = 'karyawan'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) == 0) {
        setMessage('danger', 'Karyawan tidak ditemukan!');
        redirect('admin/karyawan.php');
    }

    $karyawan = mysqli_fetch_assoc($result);

    // Cek data terkait yang akan dihapus
    $data_terkait = [];

    // Cek data presensi
    $query = "SELECT COUNT(*) as total FROM presensi WHERE user_id = '$id'";
    $result = mysqli_query($conn, $query);
    $presensi_count = mysqli_fetch_assoc($result)['total'];
    if ($presensi_count > 0) {
        $data_terkait[] = "$presensi_count data presensi";
    }

    // Cek data deteksi wajah
    $query = "SELECT COUNT(*) as total FROM deteksi_wajah WHERE user_id = '$id'";
    $result = mysqli_query($conn, $query);
    $deteksi_count = mysqli_fetch_assoc($result)['total'];
    if ($deteksi_count > 0) {
        $data_terkait[] = "$deteksi_count data deteksi wajah";
    }

    // Cek data aktivitas karyawan (jika tabel ada)
    $query = "SHOW TABLES LIKE 'aktivitas_karyawan'";
    $result = mysqli_query($conn, $query);
    if (mysqli_num_rows($result) > 0) {
        $query = "SELECT COUNT(*) as total FROM aktivitas_karyawan WHERE user_id = '$id'";
        $result = mysqli_query($conn, $query);
        $aktivitas_count = mysqli_fetch_assoc($result)['total'];
        if ($aktivitas_count > 0) {
            $data_terkait[] = "$aktivitas_count data aktivitas";
        }
    }

    // Cek data izin dinas (jika tabel ada)
    $query = "SHOW TABLES LIKE 'izin_dinas'";
    $result = mysqli_query($conn, $query);
    if (mysqli_num_rows($result) > 0) {
        $query = "SELECT COUNT(*) as total FROM izin_dinas WHERE user_id = '$id'";
        $result = mysqli_query($conn, $query);
        $izin_count = mysqli_fetch_assoc($result)['total'];
        if ($izin_count > 0) {
            $data_terkait[] = "$izin_count data izin dinas";
        }
    }

    // Cek data gangguan absensi (jika tabel ada)
    $query = "SHOW TABLES LIKE 'gangguan_absensi'";
    $result = mysqli_query($conn, $query);
    if (mysqli_num_rows($result) > 0) {
        $query = "SELECT COUNT(*) as total FROM gangguan_absensi WHERE user_id = '$id'";
        $result = mysqli_query($conn, $query);
        $gangguan_count = mysqli_fetch_assoc($result)['total'];
        if ($gangguan_count > 0) {
            $data_terkait[] = "$gangguan_count data gangguan absensi";
        }
    }

    // Cek data laporan harian berdasarkan nama
    $query = "SELECT COUNT(*) as total FROM laporan_harian WHERE nama_karyawan = '" . mysqli_real_escape_string($conn, $karyawan['nama']) . "'";
    $result = mysqli_query($conn, $query);
    $laporan_count = mysqli_fetch_assoc($result)['total'];
    if ($laporan_count > 0) {
        $data_terkait[] = "$laporan_count data laporan harian";
    }

    // Mulai transaksi untuk memastikan konsistensi data
    mysqli_begin_transaction($conn);

    try {
        // Hapus foto profil jika ada
        if (!empty($karyawan['foto_profil'])) {
            $file = UPLOAD_PATH . $karyawan['foto_profil'];
            if (file_exists($file)) {
                unlink($file);
            }
        }

        // Hapus data laporan harian berdasarkan nama (tidak ada foreign key)
        if ($laporan_count > 0) {
            $query = "DELETE FROM laporan_harian WHERE nama_karyawan = '" . mysqli_real_escape_string($conn, $karyawan['nama']) . "'";
            if (!mysqli_query($conn, $query)) {
                throw new Exception('Gagal menghapus data laporan harian: ' . mysqli_error($conn));
            }
        }

        // Hapus karyawan (data lain akan terhapus otomatis karena ON DELETE CASCADE)
        $query = "DELETE FROM users WHERE id = '$id' AND role = 'karyawan'";
        if (!mysqli_query($conn, $query)) {
            throw new Exception('Gagal menghapus data karyawan: ' . mysqli_error($conn));
        }

        // Commit transaksi
        mysqli_commit($conn);

        $pesan_data = empty($data_terkait) ? '' : ' beserta ' . implode(', ', $data_terkait);
        setMessage('success', "Karyawan {$karyawan['nama']} (NIK: {$karyawan['nik']}) berhasil dihapus$pesan_data!");

    } catch (Exception $e) {
        // Rollback jika ada error
        mysqli_rollback($conn);
        setMessage('danger', 'Gagal menghapus karyawan: ' . $e->getMessage());
    }

    redirect('admin/karyawan.php');
}

// Ambil data karyawan dengan search
$search = isset($_GET['search']) ? clean($_GET['search']) : '';

$query = "SELECT u.*, l.nama_lokasi, b.nama_bidang
          FROM users u
          LEFT JOIN lokasi l ON u.lokasi_id = l.id
          LEFT JOIN bidang b ON u.bidang_id = b.id
          WHERE u.role = 'karyawan'";

if (!empty($search)) {
    $query .= " AND (u.nik LIKE '%$search%' 
                OR u.nama LIKE '%$search%' 
                OR b.nama_bidang LIKE '%$search%' 
                OR u.jabatan LIKE '%$search%' 
                OR l.nama_lokasi LIKE '%$search%')";
}

$query .= " ORDER BY u.nama ASC";
$result = mysqli_query($conn, $query);

$karyawan = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $karyawan[] = $row;
    }
}

// Ambil data lokasi untuk dropdown
$query = "SELECT * FROM lokasi ORDER BY nama_lokasi ASC";
$result = mysqli_query($conn, $query);
$lokasi = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $lokasi[] = $row;
    }
}

// Ambil data bidang untuk dropdown
$query = "SELECT * FROM bidang ORDER BY nama_bidang ASC";
$result = mysqli_query($conn, $query);
$bidang_list = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $bidang_list[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Manajemen Karyawan</h1>
        <div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#tambahKaryawanModal" <?php echo empty($bidang_list) ? 'disabled' : ''; ?>>
                <i class="fas fa-plus"></i> Tambah Karyawan
            </button>
        </div>
    </div>

    <?php if (empty($bidang_list)): ?>
    <div class="alert alert-warning mb-4">
        <i class="fas fa-exclamation-triangle me-2"></i> Anda harus menambahkan data bidang terlebih dahulu sebelum dapat menambahkan karyawan.
        <a href="bidang.php" class="btn btn-sm btn-warning ms-2">
            <i class="fas fa-plus"></i> Tambah Bidang
        </a>
    </div>
    <?php endif; ?>

    <!-- Form Search -->
    <form method="get" action="" class="mb-3">
        <div class="input-group" style="max-width: 300px;">
            <input type="text" name="search" class="form-control" placeholder="Cari karyawan..." value="<?php echo htmlspecialchars($search); ?>">
            <button class="btn btn-primary" type="submit"><i class="fas fa-search"></i> Cari</button>
        </div>
    </form>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Data Karyawan</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>NIK</th>
                            <th>Nama</th>
                            <th>Bidang</th>
                            <th>Jabatan</th>
                            <th>Lokasi</th>
                            <th>Keterangan</th>
                            <th>Foto Profil</th>
                            <th>Absen Barcode</th>
                            <th>Absen Wajah</th>
                            <th>Jam Kerja Fleksibel</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($karyawan)): ?>
                            <tr>
                                <td colspan="11" class="text-center">Tidak ada data karyawan</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($karyawan as $k): ?>
                                <tr>
                                    <td><?php echo $k['nik']; ?></td>
                                    <td><?php echo $k['nama']; ?></td>
                                    <td><?php echo $k['nama_bidang'] ?? $k['bidang']; ?></td>
                                    <td><?php echo $k['jabatan']; ?></td>
                                    <td><?php echo $k['nama_lokasi'] ?? '-'; ?></td>
                                    <td><?php echo $k['keterangan']; ?></td>
                                    <td>
                                        <?php if (!empty($k['foto_profil'])): ?>
                                            <a href="<?php echo BASE_URL . 'uploads/' . $k['foto_profil']; ?>" target="_blank" class="btn btn-sm btn-info">
                                                <i class="fas fa-image"></i> Lihat
                                            </a>
                                        <?php else: ?> - <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if ($k['allow_barcode'] == 1): ?>
                                            <span class="badge bg-success"><i class="fas fa-check"></i> Diizinkan</span>
                                            <a href="toggle_permission.php?id=<?php echo $k['id']; ?>&type=barcode&value=0" class="btn btn-sm btn-outline-danger mt-1">
                                                <i class="fas fa-ban"></i>
                                            </a>
                                        <?php else: ?>
                                            <span class="badge bg-secondary"><i class="fas fa-times"></i> Tidak Diizinkan</span>
                                            <a href="toggle_permission.php?id=<?php echo $k['id']; ?>&type=barcode&value=1" class="btn btn-sm btn-outline-success mt-1">
                                                <i class="fas fa-check"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if ($k['allow_face'] == 1): ?>
                                            <span class="badge bg-success"><i class="fas fa-check"></i> Diizinkan</span>
                                            <a href="toggle_permission.php?id=<?php echo $k['id']; ?>&type=face&value=0" class="btn btn-sm btn-outline-danger mt-1">
                                                <i class="fas fa-ban"></i>
                                            </a>
                                        <?php else: ?>
                                            <span class="badge bg-secondary"><i class="fas fa-times"></i> Tidak Diizinkan</span>
                                            <a href="toggle_permission.php?id=<?php echo $k['id']; ?>&type=face&value=1" class="btn btn-sm btn-outline-success mt-1">
                                                <i class="fas fa-check"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if ($k['allow_flexible_schedule'] == 1): ?>
                                            <span class="badge bg-success"><i class="fas fa-check"></i> Diizinkan</span>
                                            <a href="toggle_permission.php?id=<?php echo $k['id']; ?>&type=flexible_schedule&value=0" class="btn btn-sm btn-outline-danger mt-1">
                                                <i class="fas fa-ban"></i>
                                            </a>
                                        <?php else: ?>
                                            <span class="badge bg-secondary"><i class="fas fa-times"></i> Tidak Diizinkan</span>
                                            <a href="toggle_permission.php?id=<?php echo $k['id']; ?>&type=flexible_schedule&value=1" class="btn btn-sm btn-outline-success mt-1">
                                                <i class="fas fa-check"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="edit_karyawan.php?id=<?php echo $k['id']; ?>" class="btn btn-sm btn-primary"><i class="fas fa-edit"></i> Edit</a>
                                        <a href="reset_password.php?id=<?php echo $k['id']; ?>" class="btn btn-sm btn-warning"><i class="fas fa-key"></i> Reset Password</a>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="confirmDelete(<?php echo $k['id']; ?>, '<?php echo htmlspecialchars($k['nama'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($k['nik'], ENT_QUOTES); ?>')">
                                            <i class="fas fa-trash"></i> Hapus
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Karyawan -->
<div class="modal fade add-modal" id="tambahKaryawanModal" tabindex="-1" aria-labelledby="tambahKaryawanModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tambahKaryawanModalLabel">Tambah Karyawan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="" enctype="multipart/form-data" class="add-form">
                <div class="modal-body">
                    <div class="mb-3"><label for="nik_add" class="form-label">NIK</label><input type="text" class="form-control" id="nik_add" name="nik" required></div>
                    <div class="mb-3"><label for="nama_add" class="form-label">Nama</label><input type="text" class="form-control" id="nama_add" name="nama" required></div>
                    <div class="mb-3">
                        <label for="bidang_id_add" class="form-label">Bidang</label>
                        <select class="form-select" id="bidang_id_add" name="bidang_id" required>
                            <option value="">Pilih Bidang</option>
                            <?php foreach ($bidang_list as $b): ?><option value="<?php echo $b['id']; ?>"><?php echo $b['nama_bidang']; ?></option><?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3"><label for="jabatan_add" class="form-label">Jabatan</label><input type="text" class="form-control" id="jabatan_add" name="jabatan" required></div>
                    <div class="mb-3"><label for="keterangan_add" class="form-label">Keterangan</label><textarea class="form-control" id="keterangan_add" name="keterangan" rows="3"></textarea></div>
                    <div class="mb-3">
                        <label for="lokasi_id_add" class="form-label">Lokasi</label>
                        <select class="form-select" id="lokasi_id_add" name="lokasi_id" required>
                            <option value="">Pilih Lokasi</option>
                            <?php foreach ($lokasi as $l): ?><option value="<?php echo $l['id']; ?>"><?php echo $l['nama_lokasi']; ?></option><?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3"><label for="password_add" class="form-label">Password</label><input type="password" class="form-control" id="password_add" name="password" required></div>
                    <div class="mb-3"><label for="foto_profil_add" class="form-label">Foto Profil</label><input type="file" class="form-control" id="foto_profil_add" name="foto_profil"></div>
                </div>
                <div class="modal-footer"><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button><button type="submit" name="tambah" class="btn btn-primary">Simpan</button></div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Konfirmasi Hapus -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Konfirmasi Penghapusan Karyawan
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Perhatian!</strong> Penghapusan karyawan akan menghapus SEMUA data terkait secara permanen.
                </div>

                <p><strong>Karyawan yang akan dihapus:</strong></p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-user me-2"></i><strong>Nama:</strong> <span id="deleteNama"></span></li>
                    <li><i class="fas fa-id-card me-2"></i><strong>NIK:</strong> <span id="deleteNik"></span></li>
                </ul>

                <div id="deleteDataInfo" class="mt-3">
                    <p><strong>Data yang akan dihapus:</strong></p>
                    <div id="deleteDataList" class="text-muted">
                        <i class="fas fa-spinner fa-spin me-2"></i>Memuat informasi data...
                    </div>
                </div>

                <div class="mt-3 p-3 bg-light rounded">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        Data yang dihapus tidak dapat dikembalikan. Pastikan Anda telah melakukan backup jika diperlukan.
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Batal
                </button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">
                    <i class="fas fa-trash me-1"></i>Ya, Hapus Karyawan
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id, nama, nik) {
    // Set data karyawan di modal
    document.getElementById('deleteNama').textContent = nama;
    document.getElementById('deleteNik').textContent = nik;
    document.getElementById('confirmDeleteBtn').href = 'karyawan.php?hapus=' + id;

    // Reset data info
    document.getElementById('deleteDataList').innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Memuat informasi data...';

    // Tampilkan modal
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();

    // Ambil informasi data terkait via AJAX
    fetch('get_employee_data_info.php?id=' + id)
        .then(response => response.json())
        .then(data => {
            let dataList = '';
            if (data.success && data.data_count.length > 0) {
                dataList = '<ul class="list-unstyled mb-0">';
                data.data_count.forEach(item => {
                    dataList += `<li><i class="fas fa-database me-2 text-danger"></i>${item}</li>`;
                });
                dataList += '</ul>';
                dataList += '<div class="mt-2 text-danger"><small><i class="fas fa-exclamation-triangle me-1"></i>Semua data di atas akan dihapus secara permanen!</small></div>';
            } else {
                dataList = '<div class="text-success"><i class="fas fa-check-circle me-2"></i>Tidak ada data terkait yang akan dihapus.</div>';
            }
            document.getElementById('deleteDataList').innerHTML = dataList;
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('deleteDataList').innerHTML = '<div class="text-warning"><i class="fas fa-exclamation-triangle me-2"></i>Gagal memuat informasi data. Lanjutkan dengan hati-hati.</div>';
        });
}
</script>

<?php include_once '../includes/footer.php'; ?>
