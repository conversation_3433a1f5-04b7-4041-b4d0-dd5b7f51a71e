<?php
require_once '../config/database.php';
require_once '../config/config.php';

header("Content-Type: application/vnd.ms-excel");
header("Content-Disposition: attachment; filename=Data_Lembur_" . date('Y-m-d') . ".xls");
header("Pragma: no-cache");
header("Expires: 0");

function hitungDurasi($mulai, $selesai) {
    if (!$mulai || !$selesai) return '-';
    $start = new DateTime($mulai);
    $end = new DateTime($selesai);
    $interval = $start->diff($end);
    return $interval->format('%h jam %i menit');
}

// Ambil semua data lembur
$query = "SELECT * FROM lembur ORDER BY tanggal_lembur DESC";
$result = mysqli_query($conn, $query);

echo "<table border='1'>
<thead>
<tr>
    <th>No</th>
    <th><PERSON><PERSON></th>
    <th><PERSON><PERSON></th>
    <th><PERSON></th>
    <th>Jam Selesai</th>
    <th>Durasi</th>
    <th>Keterangan</th>
    <th>Status</th>
    <th>Dibuat</th>
    <th>Diperbarui</th>
</tr>
</thead>
<tbody>";

$no = 1;
while ($row = mysqli_fetch_assoc($result)) {
    echo "<tr>
        <td>{$no}</td>
        <td>".htmlspecialchars($row['nama_karyawan'])."</td>
        <td>".date('d/m/Y', strtotime($row['tanggal_lembur']))."</td>
        <td>{$row['jam_mulai']}</td>
        <td>{$row['jam_selesai']}</td>
        <td>".hitungDurasi($row['jam_mulai'], $row['jam_selesai'])."</td>
        <td>".htmlspecialchars($row['keterangan'])."</td>
        <td>".htmlspecialchars($row['status'])."</td>
        <td>{$row['created_at']}</td>
        <td>{$row['updated_at']}</td>
    </tr>";
    $no++;
}

echo "</tbody></table>";
