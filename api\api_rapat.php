<?php
// <PERSON><PERSON><PERSON><PERSON> error untuk debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Koneksi & config
require_once '../config/database.php';
require_once '../config/config.php';

// Header & CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Validasi API key
function validateApiKey($api_key) {
    return $api_key === 'absensiku_api_key_2023';
}

// Ambil method & input
$method = $_SERVER['REQUEST_METHOD'];
$input = file_get_contents('php://input');
$data = json_decode($input, true);
if (!$data) $data = $_POST;

// API key dari GET jika tidak ada di body
if ($method === 'GET') {
    if (!isset($data['api_key']) && isset($_GET['api_key'])) {
        $data['api_key'] = $_GET['api_key'];
    }
}

// Validasi API key
if (!isset($data['api_key']) || !validateApiKey($data['api_key'])) {
    echo json_encode(['status' => 'error', 'message' => 'API key tidak valid']);
    exit;
}

// ========== GET ==========
if ($method === 'GET') {
    $sql = "SELECT * FROM rapat ORDER BY created_at DESC";
    $result = mysqli_query($conn, $sql);
    $rows = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $rows[] = $row;
    }
    echo json_encode(['status' => 'success', 'data' => $rows]);
    exit;
}

// ========== POST ==========
if ($method === 'POST') {
    $fields = [
        'judul', 'tanggal', 'waktu_mulai', 'waktu_selesai',
        'lokasi', 'deskripsi', 'barcode_value',
        'created_by', 'penanggung_jawab_id'
    ];

    $values = [];
    foreach ($fields as $f) {
        $val = mysqli_real_escape_string($conn, $data[$f] ?? '');
        $values[$f] = $val;
    }

    $now = date('Y-m-d H:i:s');
    $sql = "INSERT INTO rapat (judul, tanggal, waktu_mulai, waktu_selesai, lokasi, deskripsi, barcode_value, created_by, penanggung_jawab_id, created_at, updated_at)
            VALUES (
                '{$values['judul']}', '{$values['tanggal']}', '{$values['waktu_mulai']}', '{$values['waktu_selesai']}',
                '{$values['lokasi']}', '{$values['deskripsi']}', '{$values['barcode_value']}',
                '{$values['created_by']}', '{$values['penanggung_jawab_id']}', '$now', '$now'
            )";

    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Rapat berhasil ditambahkan']);
    } else {
        echo json_encode(['status' => 'error', 'message' => mysqli_error($conn)]);
    }
    exit;
}
?>
