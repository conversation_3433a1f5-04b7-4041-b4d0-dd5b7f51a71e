<?php
require_once '../config/config.php';
require_once '../includes/functions.php';

// Set header untuk JSON
header('Content-Type: application/json');

try {
    // Terima data JSON
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (!$data) {
        throw new Exception('Data tidak valid');
    }

    $id = clean($data['id'] ?? '');
    $type = clean($data['type'] ?? '');
    $approver_id = clean($data['approver_id'] ?? '');

    if (empty($id) || empty($type) || empty($approver_id)) {
        throw new Exception('Data tidak lengkap');
    }

    // Proses persetujuan berdasarkan tipe
    if ($type === 'gangguan_absensi') {
        // Update status gangguan absensi
        $sql = "UPDATE gangguan_absensi 
                SET status = 'Approved', 
                    approved_by = ?, 
                    approved_at = NOW(), 
                    is_notified = 0 
                WHERE id = ? AND status = 'Pending'";
        
        $stmt = $conn->prepare($sql);
        if ($stmt === false) {
            throw new Exception('Gagal mempersiapkan query: ' . $conn->error);
        }

        $stmt->bind_param("ii", $approver_id, $id);
        if (!$stmt->execute()) {
            throw new Exception('Gagal menyetujui pengajuan: ' . $stmt->error);
        }

        // Ambil data gangguan absensi
        $query = "SELECT * FROM gangguan_absensi WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $gangguan = $result->fetch_assoc();

        if ($gangguan) {
            $user_id = $gangguan['user_id'];
            $tanggal = $gangguan['tanggal'];
            $jenis_absen = $gangguan['jenis_absen'];
            $bukti_foto = $gangguan['bukti_foto'];

            // Cek apakah sudah ada data presensi
            $query = "SELECT * FROM presensi WHERE user_id = ? AND tanggal = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("is", $user_id, $tanggal);
            $stmt->execute();
            $result = $stmt->get_result();

            $lokasi = 'Kantor';
            $foto = !empty($bukti_foto) ? 'gangguan_absensi/' . $bukti_foto : 'default.jpg';

            if ($result->num_rows > 0) {
                // Update data presensi yang sudah ada
                $jam_kerja = getJamKerjaByUserId($user_id);
                $jam_masuk = $jam_kerja ? $jam_kerja['jam_masuk'] : '08:00:00';
                $jam_pulang = $jam_kerja ? $jam_kerja['jam_pulang'] : '17:00:00';

                if ($jenis_absen == 'masuk') {
                    $sql = "UPDATE presensi SET 
                            jam_masuk = ?, 
                            foto_masuk = ?, 
                            lokasi_masuk = COALESCE(lokasi_masuk, ?), 
                            status = 'Tepat Waktu', 
                            keterangan = CONCAT(COALESCE(keterangan, ''), ' (Gangguan absensi masuk)')
                            WHERE user_id = ? AND tanggal = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("sssss", $jam_masuk, $foto, $lokasi, $user_id, $tanggal);
                } else {
                    $sql = "UPDATE presensi SET 
                            jam_pulang = ?, 
                            foto_pulang = ?, 
                            lokasi_pulang = ?, 
                            status = 'Tepat Waktu', 
                            keterangan = CONCAT(COALESCE(keterangan, ''), ' (Gangguan absensi pulang)')
                            WHERE user_id = ? AND tanggal = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("sssss", $jam_pulang, $foto, $lokasi, $user_id, $tanggal);
                }
            } else {
                // Insert data presensi baru
                $jam_kerja = getJamKerjaByUserId($user_id);
                $jam_masuk = $jam_kerja ? $jam_kerja['jam_masuk'] : '08:00:00';
                $jam_pulang = $jam_kerja ? $jam_kerja['jam_pulang'] : '17:00:00';

                if ($jenis_absen == 'masuk') {
                    $sql = "INSERT INTO presensi (user_id, tanggal, jam_masuk, foto_masuk, lokasi_masuk, status, keterangan)
                            VALUES (?, ?, ?, ?, ?, 'Tepat Waktu', 'Gangguan absensi masuk')";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("issss", $user_id, $tanggal, $jam_masuk, $foto, $lokasi);
                } else {
                    $sql = "INSERT INTO presensi (user_id, tanggal, jam_pulang, foto_pulang, lokasi_pulang, status, keterangan)
                            VALUES (?, ?, ?, ?, ?, 'Tepat Waktu', 'Gangguan absensi pulang')";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("issss", $user_id, $tanggal, $jam_pulang, $foto, $lokasi);
                }
            }

            if (!$stmt->execute()) {
                throw new Exception('Gagal mengupdate data presensi: ' . $stmt->error);
            }
        }

        echo json_encode([
            'success' => true,
            'message' => 'Pengajuan berhasil disetujui dan data presensi telah diupdate'
        ]);

    } elseif ($type === 'izin_dinas') {
        $sql = "UPDATE izin_dinas 
                SET status = 'Approved', 
                    approved_by = ?, 
                    approved_at = NOW(), 
                    is_notified = 0 
                WHERE id = ? AND status = 'Pending'";
        
        $stmt = $conn->prepare($sql);
        if ($stmt === false) {
            throw new Exception('Gagal mempersiapkan query: ' . $conn->error);
        }

        $stmt->bind_param("ii", $approver_id, $id);
        if (!$stmt->execute()) {
            throw new Exception('Gagal menyetujui pengajuan: ' . $stmt->error);
        }

        echo json_encode([
            'success' => true,
            'message' => 'Pengajuan berhasil disetujui'
        ]);
    } else {
        throw new Exception('Tipe pengajuan tidak valid');
    }

} catch (Exception $e) {
    // Log error
    error_log("Error in approve_request.php: " . $e->getMessage());
    
    // Kirim response error
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?> 