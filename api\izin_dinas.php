<?php
require_once '../config/database.php';
require_once '../config/config.php';

// CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

function validateApiKey($api_key) {
    return $api_key === 'absensiku_api_key_2023';
}

// Fungsi untuk menyimpan gambar base64
function saveBase64Image($base64, $prefix = 'izin') {
    if (!$base64) return '';
    $foto_data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64));
    $foto_name = $prefix . '_' . time() . '_' . rand(1000,9999) . '.jpg';
    $upload_dir = UPLOAD_PATH;
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
    $foto_path = $upload_dir . $foto_name;
    file_put_contents($foto_path, $foto_data);
    return $foto_name;
}

$method = $_SERVER['REQUEST_METHOD'];
$input = file_get_contents('php://input');
$data = json_decode($input, true);
if (!$data) $data = $_POST;

// Untuk GET, ambil api_key dari $_GET jika tidak ada di body
if ($method === 'GET') {
    if (!isset($data['api_key']) && isset($_GET['api_key'])) {
        $data['api_key'] = $_GET['api_key'];
    }
}

if (!isset($data['api_key']) || !validateApiKey($data['api_key'])) {
    echo json_encode(['status' => 'error', 'message' => 'API key tidak valid']);
    exit;
}

// GET: List/filter izin dinas
if ($method === 'GET') {
    $where = [];
    if (isset($_GET['id'])) $where[] = "id = '" . mysqli_real_escape_string($conn, $_GET['id']) . "'";
    if (isset($_GET['user_id'])) $where[] = "user_id = '" . mysqli_real_escape_string($conn, $_GET['user_id']) . "'";
    if (isset($_GET['status'])) $where[] = "status = '" . mysqli_real_escape_string($conn, $_GET['status']) . "'";
    $sql = "SELECT id, user_id, tanggal_mulai, tanggal_selesai, tujuan, keterangan, foto_surat_tugas, foto_wajah, status, approved_by, approved_at, created_at, updated_at, is_notified FROM izin_dinas";
    if ($where) $sql .= " WHERE " . implode(' AND ', $where);
    $sql .= " ORDER BY created_at DESC";
    $result = mysqli_query($conn, $sql);
    $rows = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $rows[] = $row;
        }
    }
    echo json_encode(['status' => 'success', 'data' => $rows]);
    exit;
}

// POST: Tambah izin dinas
if ($method === 'POST') {
    $user_id = mysqli_real_escape_string($conn, $data['user_id'] ?? '');
    $tanggal_mulai = mysqli_real_escape_string($conn, $data['tanggal_mulai'] ?? '');
    $tanggal_selesai = mysqli_real_escape_string($conn, $data['tanggal_selesai'] ?? '');
    $tujuan = mysqli_real_escape_string($conn, $data['tujuan'] ?? '');
    $keterangan = mysqli_real_escape_string($conn, $data['keterangan'] ?? '');
    $status = mysqli_real_escape_string($conn, $data['status'] ?? '');
$approved_by = isset($data['approved_by']) && $data['approved_by'] !== '' 
    ? "'" . mysqli_real_escape_string($conn, $data['approved_by']) . "'" 
    : "NULL";
    $approved_at = mysqli_real_escape_string($conn, $data['approved_at'] ?? '');
    $is_notified = mysqli_real_escape_string($conn, $data['is_notified'] ?? '0');
    $created_at = date('Y-m-d H:i:s');
    $updated_at = $created_at;
    // Handle foto surat tugas base64
    $foto_surat_tugas = '';
    if (!empty($data['foto_surat_tugas_base64'])) {
        $foto_surat_tugas = saveBase64Image($data['foto_surat_tugas_base64'], 'surat_tugas');
    } else {
        $foto_surat_tugas = mysqli_real_escape_string($conn, $data['foto_surat_tugas'] ?? '');
    }
    // Handle foto wajah base64
    $foto_wajah = '';
    if (!empty($data['foto_wajah_base64'])) {
        $foto_wajah = saveBase64Image($data['foto_wajah_base64'], 'wajah');
    } else {
        $foto_wajah = mysqli_real_escape_string($conn, $data['foto_wajah'] ?? '');
    }
$sql = "INSERT INTO izin_dinas (
    user_id, tanggal_mulai, tanggal_selesai, tujuan, keterangan, 
    foto_surat_tugas, foto_wajah, status, approved_by, approved_at, 
    created_at, updated_at, is_notified
) VALUES (
    '$user_id', '$tanggal_mulai', '$tanggal_selesai', '$tujuan', '$keterangan', 
    '$foto_surat_tugas', '$foto_wajah', '$status', $approved_by, " . 
    ($approved_at ? "'$approved_at'" : "NULL") . ", 
    '$created_at', '$updated_at', '$is_notified'
)";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Izin dinas berhasil ditambahkan']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal menambah izin dinas: ' . mysqli_error($conn)]);
    }
    exit;
}

// PUT: Edit izin dinas
if ($method === 'PUT') {
    parse_str(file_get_contents('php://input'), $put_vars);
    $id = mysqli_real_escape_string($conn, $data['id'] ?? $put_vars['id'] ?? '');
    if (!$id) {
        echo json_encode(['status' => 'error', 'message' => 'ID izin dinas wajib diisi']);
        exit;
    }
    $fields = [];
    foreach ([
        'user_id', 'tanggal_mulai', 'tanggal_selesai', 'tujuan', 'keterangan', 'status', 'approved_by', 'approved_at', 'is_notified'
    ] as $col) {
        $val = $data[$col] ?? $put_vars[$col] ?? null;
        if (!is_null($val)) {
            $fields[] = "$col = '" . mysqli_real_escape_string($conn, $val) . "'";
        }
    }
    // Handle foto surat tugas base64
    if (!empty($data['foto_surat_tugas_base64'] ?? $put_vars['foto_surat_tugas_base64'] ?? '')) {
        $foto_surat_tugas = saveBase64Image($data['foto_surat_tugas_base64'] ?? $put_vars['foto_surat_tugas_base64'], 'surat_tugas');
        $fields[] = "foto_surat_tugas = '$foto_surat_tugas'";
    } elseif (!empty($data['foto_surat_tugas'] ?? $put_vars['foto_surat_tugas'] ?? '')) {
        $fields[] = "foto_surat_tugas = '" . mysqli_real_escape_string($conn, $data['foto_surat_tugas'] ?? $put_vars['foto_surat_tugas']) . "'";
    }
    // Handle foto wajah base64
    if (!empty($data['foto_wajah_base64'] ?? $put_vars['foto_wajah_base64'] ?? '')) {
        $foto_wajah = saveBase64Image($data['foto_wajah_base64'] ?? $put_vars['foto_wajah_base64'], 'wajah');
        $fields[] = "foto_wajah = '$foto_wajah'";
    } elseif (!empty($data['foto_wajah'] ?? $put_vars['foto_wajah'] ?? '')) {
        $fields[] = "foto_wajah = '" . mysqli_real_escape_string($conn, $data['foto_wajah'] ?? $put_vars['foto_wajah']) . "'";
    }
    $fields[] = "updated_at = '" . date('Y-m-d H:i:s') . "'";
    $sql = "UPDATE izin_dinas SET " . implode(', ', $fields) . " WHERE id = '$id'";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Izin dinas berhasil diupdate']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal update izin dinas: ' . mysqli_error($conn)]);
    }
    exit;
}

// DELETE: Hapus izin dinas
if ($method === 'DELETE') {
    parse_str(file_get_contents('php://input'), $del_vars);
    $id = mysqli_real_escape_string($conn, $data['id'] ?? $del_vars['id'] ?? '');
    if (!$id) {
        echo json_encode(['status' => 'error', 'message' => 'ID izin dinas wajib diisi']);
        exit;
    }
    $sql = "DELETE FROM izin_dinas WHERE id = '$id'";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Izin dinas berhasil dihapus']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal menghapus izin dinas: ' . mysqli_error($conn)]);
    }
    exit;
}

echo json_encode(['status' => 'error', 'message' => 'Metode tidak didukung']); 