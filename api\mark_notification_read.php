<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

header('Content-Type: application/json');

$response = [
    'success' => false,
    'message' => ''
];

// <PERSON>k apakah request adalah POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Ambil data dari request
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if ($data) {
        $type = isset($data['type']) ? clean($data['type']) : '';
        $request_id = isset($data['id']) ? clean($data['id']) : '';
        
        // Validasi input
        if (empty($type) || empty($request_id)) {
            $response['message'] = 'Data tidak lengkap';
            echo json_encode($response);
            exit;
        }
        
        // Tandai notifikasi sebagai telah dibaca
        if (markNotificationAsRead($type, $request_id)) {
            $response['success'] = true;
            $response['message'] = 'Notifikasi ditandai sebagai telah dibaca';
        } else {
            $response['message'] = 'Gagal menandai notifikasi';
        }
    } else {
        $response['message'] = 'Data tidak valid';
    }
} else {
    $response['message'] = 'Method tidak diizinkan';
}

echo json_encode($response);
?> 