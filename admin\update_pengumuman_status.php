<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Update status pengumuman yang sudah kadaluarsa
$today = date('Y-m-d');
$query = "UPDATE pengumuman 
          SET status = 'kadaluarsa' 
          WHERE tanggal_kadaluarsa < '$today' 
          AND status = 'aktif'";

if (mysqli_query($conn, $query)) {
    $affected_rows = mysqli_affected_rows($conn);
    if ($affected_rows > 0) {
        setMessage('success', "Berhasil mengupdate status $affected_rows pengumuman menjadi kadaluarsa");
    } else {
        setMessage('info', 'Tidak ada pengumuman yang perlu diupdate');
    }
} else {
    setMessage('danger', 'Gagal mengupdate status pengumuman: ' . mysqli_error($conn));
}

// Redirect kembali ke halaman pengumuman
redirect('pengumuman.php'); 