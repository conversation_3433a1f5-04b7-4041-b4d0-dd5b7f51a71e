# Dokumentasi: Mengecualikan Karyawan Koperasi dari Menu Tu<PERSON>ngan

## Deskripsi
Fitur ini mengecualikan karyawan yang memiliki keterangan "koperasi" dari perhitungan dan tampilan tunjangan IKK MAMIN di halaman admin. Karyawan koperasi tidak memiliki tunjangan, se<PERSON>ga mereka tidak perlu ditampilkan dalam menu tunjangan.

## Perubahan yang Dilakukan

### 1. File: `admin/tunjangan_ikk_mamin.php`
**Baris 87-101**: Modifikasi query SQL untuk mengecualikan karyawan koperasi
```sql
-- Query lama:
WHERE u.role = 'karyawan'

-- Query baru:
WHERE u.role = 'karyawan' AND (u.keterangan != 'koperasi' OR u.keterangan IS NULL)
```

### 2. File: `admin/riwayat_tunjangan.php`
**Baris 157-178**: Modifikasi query SQL untuk export Excel
**Baris 249-270**: Modifikasi query SQL untuk tampilan halaman

Kedua query diubah dengan menambahkan kondisi yang sama:
```sql
WHERE u.role = 'karyawan' AND (u.keterangan != 'koperasi' OR u.keterangan IS NULL)
```

### 3. File: `sql/add_keterangan_column.sql` (Baru)
Script SQL untuk menambahkan kolom keterangan ke tabel users jika belum ada.

### 4. File: `admin/add_keterangan_column.php` (Baru)
Script PHP untuk menjalankan penambahan kolom keterangan secara otomatis.

## Cara Kerja

1. **Identifikasi Karyawan Koperasi**: Sistem menggunakan field `keterangan` di tabel `users` dengan nilai 'koperasi' untuk mengidentifikasi karyawan koperasi.

2. **Filter Query**: Semua query yang mengambil data karyawan untuk perhitungan tunjangan ditambahkan kondisi:
   - `u.keterangan != 'koperasi'` - Mengecualikan karyawan dengan keterangan koperasi
   - `OR u.keterangan IS NULL` - Tetap menyertakan karyawan yang tidak memiliki keterangan

3. **Konsistensi**: Perubahan diterapkan pada:
   - Halaman pengaturan tunjangan (`tunjangan_ikk_mamin.php`)
   - Halaman riwayat tunjangan (`riwayat_tunjangan.php`)
   - Export Excel riwayat tunjangan

## Dampak

### Positif:
- Karyawan koperasi tidak lagi muncul di menu tunjangan
- Perhitungan tunjangan menjadi lebih akurat
- Konsistensi data antara tampilan dan export

### Tidak Ada Dampak Negatif:
- Karyawan koperasi tetap dapat menggunakan fitur absensi normal
- Data karyawan koperasi tetap tersimpan di database
- Laporan khusus karyawan koperasi (`laporan_koperasi.php`) tetap berfungsi normal

## Testing

Untuk memastikan fitur berjalan dengan baik:

1. **Buat karyawan test dengan keterangan 'koperasi'**
2. **Akses menu Tunjangan > Pengaturan Tunjangan**
   - Pastikan karyawan koperasi tidak muncul dalam daftar
3. **Akses menu Tunjangan > Riwayat Tunjangan**
   - Pastikan karyawan koperasi tidak muncul dalam daftar
   - Test export Excel, pastikan karyawan koperasi tidak termasuk
4. **Akses Laporan > Laporan Koperasi**
   - Pastikan karyawan koperasi masih muncul di laporan khusus ini

## Maintenance

Jika ada file baru yang menampilkan data tunjangan, pastikan untuk menambahkan kondisi yang sama:
```sql
WHERE u.role = 'karyawan' AND (u.keterangan != 'koperasi' OR u.keterangan IS NULL)
```
