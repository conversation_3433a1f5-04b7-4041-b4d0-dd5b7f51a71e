<?php
require_once '../config/database.php';
require_once '../config/config.php';

// ---------- <PERSON><PERSON><PERSON> tidak ada output sebelum header ----------
// ---------- CORS (Cross-Origin) ----------
$allowed_origins = '*';
$allowed_methods = 'GET, POST, PUT, DELETE, OPTIONS';
$allowed_headers = 'Content-Type, Authorization, X-Requested-With, X-API-KEY, Api-Key, Accept, Origin';

header("Access-Control-Allow-Origin: {$allowed_origins}");
header("Access-Control-Allow-Methods: {$allowed_methods}");
header("Access-Control-Allow-Headers: {$allowed_headers}");
header("Access-Control-Allow-Credentials: true");
header("Access-Control-Max-Age: 86400");
header('Content-Type: application/json; charset=UTF-8');

// Jika ada preflight request yang meminta header spesifik, beri juga header tersebut
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    if (!empty($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) {
        header('Access-Control-Allow-Headers: ' . $_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']);
    }
    http_response_code(200);
    exit();
}

// ---------- Helper: dapatkan header case-insensitive ----------
function get_header_value($name) {
    $headers = [];
    if (function_exists('getallheaders')) {
        $headers = getallheaders();
    } else {
        foreach ($_SERVER as $k => $v) {
            if (substr($k, 0, 5) === 'HTTP_') {
                $h = str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($k,5)))));
                $headers[$h] = $v;
            }
        }
    }
    foreach ($headers as $k => $v) {
        if (strtolower($k) === strtolower($name)) return $v;
    }
    return null;
}

// ---------- Pastikan UPLOAD_PATH ada (fallback) ----------
if (!defined('UPLOAD_PATH')) {
    $default_upload_dir = __DIR__ . '/../uploads/';
    if (!file_exists($default_upload_dir)) mkdir($default_upload_dir, 0777, true);
    define('UPLOAD_PATH', $default_upload_dir);
}

// ---------- Konfigurasi API Key valid ----------
$VALID_API_KEY = 'absensiku_api_key_2023';

// ---------- Fungsi simpan gambar base64 ----------
function saveBase64Image($base64, $prefix = 'foto') {
    if (!$base64) return '';
    $foto_data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64));
    if ($foto_data === false) return '';
    $foto_name = $prefix . '_' . time() . '_' . rand(1000,9999) . '.jpg';
    $upload_dir = rtrim(UPLOAD_PATH, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR;
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
    $foto_path = $upload_dir . $foto_name;
    if (file_put_contents($foto_path, $foto_data) === false) return '';
    return $foto_name;
}

// ---------- Ambil request body (JSON) ----------
$method = $_SERVER['REQUEST_METHOD'];
$raw   = file_get_contents('php://input');
$body  = json_decode($raw, true);
if (!is_array($body)) $body = $_POST ?? [];

// ---------- Ambil API key (dari query, body, header X-API-KEY, atau Authorization Bearer) ----------
$api_key = $_GET['api_key'] ?? $body['api_key'] ?? null;
if (!$api_key) {
    $api_key = get_header_value('X-API-KEY') ?? get_header_value('Api-Key') ?? null;
}
if (!$api_key) {
    $auth = get_header_value('Authorization') ?? get_header_value('authorization');
    if ($auth) {
        // jika format "Bearer KEY"
        if (stripos($auth, 'Bearer ') === 0) $api_key = substr($auth, 7);
        else $api_key = $auth;
    }
}

// Cek API key (untuk non-OPTIONS requests)
if ($api_key !== $VALID_API_KEY) {
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => 'API key tidak valid']);
    exit;
}

// ---------- ROUTING: GET (list / filter lembur) ----------
if ($method === 'GET') {
    $where = [];
    if (isset($_GET['id'])) $where[] = "id = '" . mysqli_real_escape_string($conn, $_GET['id']) . "'";
    if (isset($_GET['nama_karyawan'])) $where[] = "nama_karyawan = '" . mysqli_real_escape_string($conn, $_GET['nama_karyawan']) . "'";
    if (isset($_GET['tanggal_lembur'])) $where[] = "tanggal_lembur = '" . mysqli_real_escape_string($conn, $_GET['tanggal_lembur']) . "'";
    $sql = "SELECT id, nama_karyawan, keterangan, tanggal_lembur, jam_mulai, foto_mulai, jam_selesai, foto_selesai, status, created_at, updated_at FROM lembur";
    if ($where) $sql .= " WHERE " . implode(' AND ', $where);
    $sql .= " ORDER BY tanggal_lembur DESC, jam_mulai ASC";
    $res = mysqli_query($conn, $sql);
    $rows = [];
    if ($res) {
        while ($r = mysqli_fetch_assoc($res)) $rows[] = $r;
    }
    echo json_encode(['status' => 'success', 'data' => $rows]);
    exit;
}

// ---------- ROUTING: POST (tambah lembur - jam mulai) ----------
if ($method === 'POST') {
    $nama_karyawan = mysqli_real_escape_string($conn, $body['nama_karyawan'] ?? '');
    $keterangan    = mysqli_real_escape_string($conn, $body['keterangan'] ?? '');
    $tanggal       = mysqli_real_escape_string($conn, $body['tanggal_lembur'] ?? date('Y-m-d'));
    $jam_mulai     = mysqli_real_escape_string($conn, $body['jam_mulai'] ?? '');
    $foto_mulai    = '';

    if (!empty($body['foto_mulai_base64'])) {
        $foto_mulai = saveBase64Image($body['foto_mulai_base64'], 'foto_mulai');
    } else {
        $foto_mulai = mysqli_real_escape_string($conn, $body['foto_mulai'] ?? '');
    }

    if (!$nama_karyawan || !$jam_mulai || !$foto_mulai) {
        echo json_encode(['status' => 'error', 'message' => 'Data kurang lengkap']);
        exit;
    }

    $created_at = date('Y-m-d H:i:s');
    $sql = "INSERT INTO lembur (nama_karyawan, keterangan, tanggal_lembur, jam_mulai, foto_mulai, status, created_at) VALUES ('$nama_karyawan', '$keterangan', '$tanggal', '$jam_mulai', '$foto_mulai', 'Menunggu', '$created_at')";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Lembur berhasil diajukan']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal menambah lembur: ' . mysqli_error($conn)]);
    }
    exit;
}

// ---------- ROUTING: PUT (update lembur - jam selesai) ----------
if ($method === 'PUT') {
    parse_str(file_get_contents('php://input'), $put_vars);
    $id = mysqli_real_escape_string($conn, $body['id'] ?? $put_vars['id'] ?? '');
    if (!$id) {
        echo json_encode(['status' => 'error', 'message' => 'ID lembur wajib diisi']);
        exit;
    }

    $jam_selesai = mysqli_real_escape_string($conn, $body['jam_selesai'] ?? $put_vars['jam_selesai'] ?? '');
    $foto_selesai = '';

    if (!empty($body['foto_selesai_base64'] ?? $put_vars['foto_selesai_base64'] ?? '')) {
        $foto_selesai = saveBase64Image($body['foto_selesai_base64'] ?? $put_vars['foto_selesai_base64'], 'foto_selesai');
    } elseif (!empty($body['foto_selesai'] ?? $put_vars['foto_selesai'] ?? '')) {
        $foto_selesai = mysqli_real_escape_string($conn, $body['foto_selesai'] ?? $put_vars['foto_selesai']);
    }

    if (!$jam_selesai || !$foto_selesai) {
        echo json_encode(['status' => 'error', 'message' => 'Data kurang lengkap']);
        exit;
    }

    $updated_at = date('Y-m-d H:i:s');
    $sql = "UPDATE lembur SET jam_selesai='$jam_selesai', foto_selesai='$foto_selesai', status='selesai', updated_at='$updated_at' WHERE id='$id'";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Lembur berhasil diperbarui']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal update lembur: ' . mysqli_error($conn)]);
    }
    exit;
}

// ---------- ROUTING: DELETE (hapus lembur) ----------
if ($method === 'DELETE') {
    parse_str(file_get_contents('php://input'), $del_vars);
    $id = mysqli_real_escape_string($conn, $body['id'] ?? $del_vars['id'] ?? '');
    if (!$id) {
        echo json_encode(['status' => 'error', 'message' => 'ID lembur wajib diisi']);
        exit;
    }
    $sql = "DELETE FROM lembur WHERE id = '$id'";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Data lembur berhasil dihapus']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal menghapus data lembur: ' . mysqli_error($conn)]);
    }
    exit;
}

echo json_encode(['status' => 'error', 'message' => 'Metode tidak didukung']);
