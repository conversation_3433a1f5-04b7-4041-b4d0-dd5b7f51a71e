<?php
session_start();
include_once '../config/config.php';
include_once '../config/database.php';

// Pastikan hanya admin yang bisa mengakses
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ' . BASE_URL . 'index.php');
    exit();
}

// Ambil bulan dan tahun dari parameter GET atau gunakan bulan/tahun saat ini
$bulan = isset($_GET['bulan']) ? $_GET['bulan'] : date('m');
$tahun = isset($_GET['tahun']) ? $_GET['tahun'] : date('Y');

// Set header untuk export HTML ke Excel
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment; filename="laporan_koperasi_absensi_"' . $bulan . '-' . $tahun . '.xls"');
header('Pragma: no-cache');
header('Expires: 0');

echo '<html>';
echo '<head>';
echo '<meta charset="UTF-8">';
echo '<title>Laporan Absensi Koperasi Bulan ' . $bulan . ' Tahun ' . $tahun . '</title>';
echo '<style>';
echo 'h2, p { text-align: center; }';
echo 'table { width: 100%; border-collapse: collapse; }';
echo 'th, td { padding: 8px; text-align: left; }';
echo '</style>';
echo '</head>';
echo '<body>';
echo '<h2>Laporan Absensi Karyawan Koperasi</h2>';
echo '<p>Periode: ' . date('F Y', mktime(0, 0, 0, $bulan, 1, $tahun)) . '</p>';
echo '<table border="1">';
echo '<thead>';
echo '<tr>';
echo '<th>NIK</th>';
echo '<th>Nama</th>';
echo '<th>Bidang</th>';
echo '<th>Jabatan</th>';
echo '<th>Hadir</th>';
echo '<th>Terlambat</th>';
echo '<th>Pulang Awal</th>';
echo '<th>Lembur</th>';
echo '<th>Tidak Absen</th>';
echo '<th>Total Hari Kerja</th>';
echo '<th>Total Denda</th>';
echo '</tr>';
echo '</thead>';
echo '<tbody>';

// Hitung total hari kerja per bidang
$total_hari_kerja_per_bidang = [];
$first_day = mktime(0, 0, 0, $bulan, 1, $tahun);
$last_day = mktime(0, 0, 0, $bulan + 1, 0, $tahun);

// Ambil data hari libur untuk bulan ini
$sql_libur = "SELECT tanggal FROM hari_libur 
              WHERE MONTH(tanggal) = '$bulan' AND YEAR(tanggal) = '$tahun'";
$result_libur = mysqli_query($conn, $sql_libur);
$hari_libur = [];
while ($row_libur = mysqli_fetch_assoc($result_libur)) {
    $hari_libur[] = $row_libur['tanggal'];
}

// Ambil semua bidang
$query_bidang = "SELECT id FROM bidang";
$result_bidang = mysqli_query($conn, $query_bidang);
while ($row_bidang = mysqli_fetch_assoc($result_bidang)) {
    $bidang_id = $row_bidang['id'];
    $total_hari_kerja_bulan = 0;
    
    // Hitung hari kerja untuk setiap hari dalam bulan
    for ($i = $first_day; $i <= $last_day; $i += 86400) {
        $current_date = date('Y-m-d', $i);
        
        // Skip jika hari ini adalah hari libur
        if (in_array($current_date, $hari_libur)) {
            continue;
        }
        
        $hari = date('N', $i); // 1 (Senin) sampai 7 (Minggu)
        $sql_check = "SELECT status FROM hari_kerja 
                     WHERE bidang_id = '$bidang_id' 
                     AND hari = CASE 
                        WHEN $hari = 1 THEN 'Senin'
                        WHEN $hari = 2 THEN 'Selasa'
                        WHEN $hari = 3 THEN 'Rabu'
                        WHEN $hari = 4 THEN 'Kamis'
                        WHEN $hari = 5 THEN 'Jumat'
                        WHEN $hari = 6 THEN 'Sabtu'
                        WHEN $hari = 7 THEN 'Minggu'
                     END";
        $result_check = mysqli_query($conn, $sql_check);
        $row_check = mysqli_fetch_assoc($result_check);
        if ($row_check && $row_check['status'] == 1) {
            $total_hari_kerja_bulan++;
        }
    }
    
    $total_hari_kerja_per_bidang[$bidang_id] = $total_hari_kerja_bulan;
}

// Query untuk mengambil data karyawan koperasi
$query = "SELECT u.*, b.id as bidang_id, b.nama_bidang 
          FROM users u 
          LEFT JOIN bidang b ON u.bidang_id = b.id 
          WHERE u.keterangan = 'koperasi' 
          ORDER BY u.nama ASC";
$result = mysqli_query($conn, $query);

if ($result) {
    while ($karyawan = mysqli_fetch_assoc($result)) {
        // Ambil total hari kerja berdasarkan bidang
        $bidang_id = $karyawan['bidang_id'];
        $total_hari_kerja = isset($total_hari_kerja_per_bidang[$bidang_id]) ? 
                           $total_hari_kerja_per_bidang[$bidang_id] : 0;
        
        // Hitung statistik absensi
        $hadir = 0;
        $terlambat = 0;
        $pulang_awal = 0;
        $lembur = 0;
        
        // Query untuk menghitung statistik absensi
        $query_presensi = "SELECT status, COUNT(*) as jumlah 
                           FROM presensi 
                           WHERE user_id = '{$karyawan['id']}' 
                           AND MONTH(tanggal) = '$bulan' 
                           AND YEAR(tanggal) = '$tahun'
                           GROUP BY status";
        $result_presensi = mysqli_query($conn, $query_presensi);
        
        while ($row_presensi = mysqli_fetch_assoc($result_presensi)) {
            switch ($row_presensi['status']) {
                case 'Tepat Waktu':
                    $hadir = $row_presensi['jumlah'];
                    break;
                case 'Terlambat':
                    $terlambat = $row_presensi['jumlah'];
                    break;
                case 'Pulang Awal':
                    $pulang_awal = $row_presensi['jumlah'];
                    break;
                case 'Lembur':
                    $lembur = $row_presensi['jumlah'];
                    break;
            }
        }
        
        // Hitung tidak absen (berdasarkan hari kerja per bidang)
        $tidak_absen = $total_hari_kerja - ($hadir + $terlambat + $pulang_awal + $lembur);
        if ($tidak_absen < 0) $tidak_absen = 0;
        
        // Hitung total denda (Rp 3.000 per hari tidak absen)
        $total_denda = $tidak_absen * 3000;

        echo '<tr>';
        echo '<td>' . $karyawan['nik'] . '</td>';
        echo '<td>' . $karyawan['nama'] . '</td>';
        echo '<td>' . $karyawan['nama_bidang'] . '</td>';
        echo '<td>' . $karyawan['jabatan'] . '</td>';
        echo '<td>' . $hadir . '</td>';
        echo '<td>' . $terlambat . '</td>';
        echo '<td>' . $pulang_awal . '</td>';
        echo '<td>' . $lembur . '</td>';
        echo '<td>' . $tidak_absen . '</td>';
        echo '<td>' . $total_hari_kerja . '</td>';
        echo '<td>Rp ' . number_format($total_denda, 0, ',', '.') . '</td>';
        echo '</tr>';
    }
}

echo '</tbody>';
echo '</table>';
echo '</body>';
echo '</html>';
exit();
?> 