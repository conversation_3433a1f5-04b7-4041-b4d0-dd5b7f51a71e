<?php
// ✅ Header CORS agar support cross-origin request
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");

// ✅ Tangani preflight request OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// API Karyawan untuk menu admin
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

header('Content-Type: application/json');

// Fungsi validasi API key
function validateApiKey($api_key) {
    return $api_key === 'absensiku_api_key_2023';
}

// Fungsi upload foto profil dari base64
function saveProfilePhoto($base64, $nik) {
    if (!$base64) return null;
    $foto_data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64));
    $foto_name = 'profile_' . $nik . '_' . time() . '.jpg';
    $upload_dir = UPLOAD_PATH;
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
    $foto_path = $upload_dir . $foto_name;
    file_put_contents($foto_path, $foto_data);
    return $foto_name;
}

// Ambil data dari request
$method = $_SERVER['REQUEST_METHOD'];
$input = file_get_contents('php://input');
$data = json_decode($input, true);
if (!$data) $data = $_POST;

// Perbaikan: Untuk GET, ambil api_key dari $_GET jika tidak ada di body
if ($method === 'GET') {
    // Perbaikan: Untuk GET, ambil api_key dari $_GET jika tidak ada di body
    if (!isset($data['api_key']) && isset($_GET['api_key'])) {
        $data['api_key'] = $_GET['api_key'];
    }
    // Jika ada parameter nik dan password, lakukan autentikasi
    if (isset($_GET['nik']) && isset($_GET['password'])) {
        $nik = clean($_GET['nik']);
        $password = $_GET['password'];
        $query = "SELECT * FROM users WHERE nik = '$nik' AND role = 'karyawan'";
        $result = mysqli_query($conn, $query);
        if ($result && mysqli_num_rows($result) > 0) {
            $user = mysqli_fetch_assoc($result);
            if (password_verify($password, $user['password'])) {
                unset($user['password']); // Jangan tampilkan hash password
                echo json_encode(['status' => 'success', 'data' => $user]);
                exit;
            } else {
                echo json_encode(['status' => 'error', 'message' => 'Password salah']);
                exit;
            }
        } else {
            echo json_encode(['status' => 'error', 'message' => 'NIK tidak ditemukan']);
            exit;
        }
    }
    // Jika tidak ada nik/password, tampilkan list karyawan
    $karyawan = getAllKaryawan();
    echo json_encode(['status' => 'success', 'data' => $karyawan]);
    exit;
}

// POST: Tambah karyawan
if ($method === 'POST') {
    $nik = clean($data['nik'] ?? '');
    $nama = clean($data['nama'] ?? '');
    $bidang_id = clean($data['bidang_id'] ?? '');
    $jabatan = clean($data['jabatan'] ?? '');
    $lokasi_id = clean($data['lokasi_id'] ?? '');
    $password = clean($data['password'] ?? '');
    $foto_profil = null;
    if (!empty($data['foto_profil_base64'])) {
        $foto_profil = saveProfilePhoto($data['foto_profil_base64'], $nik);
    }
    if (registerKaryawan($nik, $nama, $bidang_id, $jabatan, $lokasi_id, $password, $foto_profil)) {
        echo json_encode(['status' => 'success', 'message' => 'Karyawan berhasil ditambahkan']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal menambahkan karyawan! NIK mungkin sudah terdaftar atau bidang tidak valid.']);
    }
    exit;
}

// PUT: Edit karyawan
if ($method === 'PUT') {
    parse_str(file_get_contents('php://input'), $put_vars);
    $id = clean($data['id'] ?? $put_vars['id'] ?? '');
    if (!$id) {
        echo json_encode(['status' => 'error', 'message' => 'ID karyawan wajib diisi']);
        exit;
    }
    $nik = clean($data['nik'] ?? $put_vars['nik'] ?? '');
    $nama = clean($data['nama'] ?? $put_vars['nama'] ?? '');
    $bidang_id = clean($data['bidang_id'] ?? $put_vars['bidang_id'] ?? '');
    $jabatan = clean($data['jabatan'] ?? $put_vars['jabatan'] ?? '');
    $lokasi_id = clean($data['lokasi_id'] ?? $put_vars['lokasi_id'] ?? '');
    $keterangan = clean($data['keterangan'] ?? $put_vars['keterangan'] ?? '');
    $foto_profil = null;
    if (!empty($data['foto_profil_base64'] ?? $put_vars['foto_profil_base64'] ?? '')) {
        $foto_profil = saveProfilePhoto($data['foto_profil_base64'] ?? $put_vars['foto_profil_base64'], $nik);
    }
    $query = "UPDATE users SET nik='$nik', nama='$nama', bidang_id='$bidang_id', jabatan='$jabatan', lokasi_id='$lokasi_id', keterangan='$keterangan'";
    if ($foto_profil) {
        $query .= ", foto_profil='$foto_profil'";
    }
    $query .= " WHERE id='$id' AND role='karyawan'";
    if (mysqli_query($conn, $query)) {
        echo json_encode(['status' => 'success', 'message' => 'Karyawan berhasil diupdate']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal mengupdate karyawan: ' . mysqli_error($conn)]);
    }
    exit;
}

// DELETE: Hapus karyawan
if ($method === 'DELETE') {
    parse_str(file_get_contents('php://input'), $del_vars);
    $id = clean($data['id'] ?? $del_vars['id'] ?? '');
    if (!$id) {
        echo json_encode(['status' => 'error', 'message' => 'ID karyawan wajib diisi']);
        exit;
    }

    // Ambil data karyawan untuk konfirmasi
    $query = "SELECT * FROM users WHERE id = '$id' AND role = 'karyawan'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) == 0) {
        echo json_encode(['status' => 'error', 'message' => 'Karyawan tidak ditemukan']);
        exit;
    }

    $karyawan = mysqli_fetch_assoc($result);

    // Mulai transaksi untuk memastikan konsistensi data
    mysqli_begin_transaction($conn);

    try {
        // Hapus foto profil jika ada
        if (!empty($karyawan['foto_profil'])) {
            $file = UPLOAD_PATH . $karyawan['foto_profil'];
            if (file_exists($file)) {
                unlink($file);
            }
        }

        // Hapus data laporan harian berdasarkan nama (tidak ada foreign key)
        $query = "DELETE FROM laporan_harian WHERE nama_karyawan = '" . mysqli_real_escape_string($conn, $karyawan['nama']) . "'";
        mysqli_query($conn, $query); // Tidak perlu cek error karena mungkin tidak ada data

        // Hapus karyawan (data lain akan terhapus otomatis karena ON DELETE CASCADE)
        $query = "DELETE FROM users WHERE id = '$id' AND role = 'karyawan'";
        if (!mysqli_query($conn, $query)) {
            throw new Exception('Gagal menghapus data karyawan: ' . mysqli_error($conn));
        }

        // Commit transaksi
        mysqli_commit($conn);

        echo json_encode(['status' => 'success', 'message' => "Karyawan {$karyawan['nama']} (NIK: {$karyawan['nik']}) berhasil dihapus beserta semua data terkait"]);

    } catch (Exception $e) {
        // Rollback jika ada error
        mysqli_rollback($conn);
        echo json_encode(['status' => 'error', 'message' => 'Gagal menghapus karyawan: ' . $e->getMessage()]);
    }
    exit;
}

// Metode tidak didukung
echo json_encode(['status' => 'error', 'message' => 'Metode tidak didukung']);
