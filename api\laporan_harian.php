<?php
require_once '../config/database.php';
require_once '../config/config.php';

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

function validateApiKey($api_key) {
    return $api_key === 'absensiku_api_key_2023';
}

// Fungsi simpan base64 ke file
function saveBase64Image($base64, $folder = '../uploads') {
    if (empty($base64)) return null;

    if (preg_match('/^data:image\/(\w+);base64,/', $base64, $type)) {
        $base64 = substr($base64, strpos($base64, ',') + 1);
        $extension = strtolower($type[1]);

        if (!in_array($extension, ['jpg', 'jpeg', 'png'])) {
            return null;
        }

        $base64 = str_replace(' ', '+', $base64);
        $imageData = base64_decode($base64);

        if ($imageData === false) return null;

        $filename = uniqid('foto_', true) . '.' . $extension;

        if (!file_exists($folder)) {
            mkdir($folder, 0755, true);
        }

        $filePath = $folder . '/' . $filename;

        if (file_put_contents($filePath, $imageData)) {
            return $filename;
        }
    }

    return null;
}

$method = $_SERVER['REQUEST_METHOD'];
$input = file_get_contents('php://input');
$data = json_decode($input, true);
if (!$data) $data = $_POST;

if ($method === 'GET') {
    if (!isset($data['api_key']) && isset($_GET['api_key'])) {
        $data['api_key'] = $_GET['api_key'];
    }
}

if (!isset($data['api_key']) || !validateApiKey($data['api_key'])) {
    echo json_encode(['status' => 'error', 'message' => 'API key tidak valid']);
    exit;
}

// GET
if ($method === 'GET') {
    $where = [];
    if (isset($_GET['id'])) $where[] = "id = '" . mysqli_real_escape_string($conn, $_GET['id']) . "'";
    if (isset($_GET['nama_karyawan'])) $where[] = "nama_karyawan LIKE '%" . mysqli_real_escape_string($conn, $_GET['nama_karyawan']) . "%'";
    if (isset($_GET['periode'])) $where[] = "periode = '" . mysqli_real_escape_string($conn, $_GET['periode']) . "'";

    $sql = "SELECT * FROM laporan_harian";
    if ($where) $sql .= " WHERE " . implode(' AND ', $where);
    $sql .= " ORDER BY tanggal DESC";

    $result = mysqli_query($conn, $sql);
    $rows = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $rows[] = $row;
        }
    }
    echo json_encode(['status' => 'success', 'data' => $rows]);
    exit;
}

// POST
if ($method === 'POST') {
    $nama = mysqli_real_escape_string($conn, $data['nama_karyawan'] ?? '');
    $periode = mysqli_real_escape_string($conn, $data['periode'] ?? '');
    $tanggal = mysqli_real_escape_string($conn, $data['tanggal'] ?? '');
    $keterangan = mysqli_real_escape_string($conn, $data['keterangan'] ?? '');
    $foto_base64 = $data['foto'] ?? '';
    $foto_file = saveBase64Image($foto_base64);

    $created_at = date('Y-m-d H:i:s');
    $updated_at = $created_at;

    $sql = "INSERT INTO laporan_harian (nama_karyawan, periode, tanggal, keterangan, foto, created_at, updated_at)
            VALUES ('$nama', '$periode', '$tanggal', '$keterangan', '$foto_file', '$created_at', '$updated_at')";

    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Laporan berhasil ditambahkan']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal menambahkan laporan: ' . mysqli_error($conn)]);
    }
    exit;
}

// PUT
if ($method === 'PUT') {
    parse_str(file_get_contents('php://input'), $put_vars);
    $id = mysqli_real_escape_string($conn, $data['id'] ?? $put_vars['id'] ?? '');
    if (!$id) {
        echo json_encode(['status' => 'error', 'message' => 'ID laporan wajib diisi']);
        exit;
    }

    $fields = [];
    foreach (['nama_karyawan', 'periode', 'tanggal', 'keterangan', 'foto'] as $col) {
        $val = $data[$col] ?? $put_vars[$col] ?? null;
        if (!is_null($val)) {
            if ($col === 'foto') {
                $foto_file = saveBase64Image($val);
                if ($foto_file) {
                    $fields[] = "foto = '" . mysqli_real_escape_string($conn, $foto_file) . "'";
                }
            } else {
                $fields[] = "$col = '" . mysqli_real_escape_string($conn, $val) . "'";
            }
        }
    }

    $fields[] = "updated_at = '" . date('Y-m-d H:i:s') . "'";
    $sql = "UPDATE laporan_harian SET " . implode(', ', $fields) . " WHERE id = '$id'";

    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Laporan berhasil diupdate']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal update laporan: ' . mysqli_error($conn)]);
    }
    exit;
}

// DELETE
if ($method === 'DELETE') {
    parse_str(file_get_contents('php://input'), $del_vars);
    $id = mysqli_real_escape_string($conn, $data['id'] ?? $del_vars['id'] ?? '');
    if (!$id) {
        echo json_encode(['status' => 'error', 'message' => 'ID laporan wajib diisi']);
        exit;
    }

    $sql = "DELETE FROM laporan_harian WHERE id = '$id'";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Laporan berhasil dihapus']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal menghapus laporan: ' . mysqli_error($conn)]);
    }
    exit;
}

echo json_encode(['status' => 'error', 'message' => 'Metode tidak didukung']);
