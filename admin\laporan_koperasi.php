<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Cek akses
checkAccess('admin');

// Inisialisasi variabel
$bulan = isset($_GET['bulan']) ? $_GET['bulan'] : date('m');
$tahun = isset($_GET['tahun']) ? $_GET['tahun'] : date('Y');

// Hitung total hari kerja per bidang
$total_hari_kerja_per_bidang = [];
$first_day = mktime(0, 0, 0, $bulan, 1, $tahun);
$last_day = mktime(0, 0, 0, $bulan + 1, 0, $tahun);

// Ambil data hari libur untuk bulan ini
$sql_libur = "SELECT tanggal FROM hari_libur 
              WHERE MONTH(tanggal) = '$bulan' AND YEAR(tanggal) = '$tahun'";
$result_libur = mysqli_query($conn, $sql_libur);
$hari_libur = [];
while ($row_libur = mysqli_fetch_assoc($result_libur)) {
    $hari_libur[] = $row_libur['tanggal'];
}

// Ambil semua bidang
$query_bidang = "SELECT id FROM bidang";
$result_bidang = mysqli_query($conn, $query_bidang);
while ($row_bidang = mysqli_fetch_assoc($result_bidang)) {
    $bidang_id = $row_bidang['id'];
    $total_hari_kerja_bulan = 0;
    
    // Hitung hari kerja untuk setiap hari dalam bulan
    for ($i = $first_day; $i <= $last_day; $i += 86400) {
        $current_date = date('Y-m-d', $i);
        
        // Skip jika hari ini adalah hari libur
        if (in_array($current_date, $hari_libur)) {
            continue;
        }
        
        $hari = date('N', $i); // 1 (Senin) sampai 7 (Minggu)
        $sql_check = "SELECT status FROM hari_kerja 
                     WHERE bidang_id = '$bidang_id' 
                     AND hari = CASE 
                        WHEN $hari = 1 THEN 'Senin'
                        WHEN $hari = 2 THEN 'Selasa'
                        WHEN $hari = 3 THEN 'Rabu'
                        WHEN $hari = 4 THEN 'Kamis'
                        WHEN $hari = 5 THEN 'Jumat'
                        WHEN $hari = 6 THEN 'Sabtu'
                        WHEN $hari = 7 THEN 'Minggu'
                     END";
        $result_check = mysqli_query($conn, $sql_check);
        $row_check = mysqli_fetch_assoc($result_check);
        if ($row_check && $row_check['status'] == 1) {
            $total_hari_kerja_bulan++;
        }
    }
    
    $total_hari_kerja_per_bidang[$bidang_id] = $total_hari_kerja_bulan;
}

// Query untuk mengambil data karyawan koperasi
$query = "SELECT u.*, b.id as bidang_id, b.nama_bidang 
          FROM users u 
          LEFT JOIN bidang b ON u.bidang_id = b.id 
          WHERE u.keterangan = 'koperasi' 
          ORDER BY u.nama ASC";
$result = mysqli_query($conn, $query);

$karyawan_koperasi = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $karyawan_koperasi[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Laporan Absensi Karyawan Koperasi</h1>

    <!-- Filter Bulan dan Tahun -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Filter Laporan</h6>
        </div>
        <div class="card-body">
            <form method="get" action="" class="form-inline">
                <div class="form-group mr-2">
                    <label for="bulan" class="mr-2">Bulan:</label>
                    <select name="bulan" id="bulan" class="form-control">
                        <?php
                        $nama_bulan = [
                            '01' => 'Januari', '02' => 'Februari', '03' => 'Maret',
                            '04' => 'April', '05' => 'Mei', '06' => 'Juni',
                            '07' => 'Juli', '08' => 'Agustus', '09' => 'September',
                            '10' => 'Oktober', '11' => 'November', '12' => 'Desember'
                        ];
                        foreach ($nama_bulan as $key => $value) {
                            $selected = ($key == $bulan) ? 'selected' : '';
                            echo "<option value='$key' $selected>$value</option>";
                        }
                        ?>
                    </select>
                </div>
                <div class="form-group mr-2">
                    <label for="tahun" class="mr-2">Tahun:</label>
                    <select name="tahun" id="tahun" class="form-control">
                        <?php
                        $tahun_mulai = 2020;
                        $tahun_sekarang = date('Y');
                        for ($t = $tahun_sekarang; $t >= $tahun_mulai; $t--) {
                            $selected = ($t == $tahun) ? 'selected' : '';
                            echo "<option value='$t' $selected>$t</option>";
                        }
                        ?>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">Tampilkan</button>
            </form>
            <hr>
            <a href="export_laporan_koperasi.php?bulan=<?php echo $bulan; ?>&tahun=<?php echo $tahun; ?>" class="btn btn-success mt-3">
                <i class="fas fa-file-excel"></i> Export Excel
            </a>
        </div>
    </div>

    <!-- Tabel Laporan -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Data Absensi Karyawan Koperasi</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>NIK</th>
                            <th>Nama</th>
                            <th>Bidang</th>
                            <th>Jabatan</th>
                            <th>Hadir</th>
                            <th>Terlambat</th>
                            <th>Pulang Awal</th>
                            <th>Lembur</th>
                            <th>Tidak Absen</th>
                            <th>Total Hari Kerja</th>
                            <th>Total Denda</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($karyawan_koperasi)): ?>
                            <tr>
                                <td colspan="11" class="text-center">Tidak ada data karyawan koperasi</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($karyawan_koperasi as $karyawan): ?>
                                <?php
                                // Ambil total hari kerja berdasarkan bidang
                                $bidang_id = $karyawan['bidang_id'];
                                $total_hari_kerja = isset($total_hari_kerja_per_bidang[$bidang_id]) ? 
                                                   $total_hari_kerja_per_bidang[$bidang_id] : 0;
                                
                                // Hitung statistik absensi
                                $hadir = 0;
                                $terlambat = 0;
                                $pulang_awal = 0;
                                $lembur = 0;
                                
                                // Query untuk menghitung statistik absensi
                                $query = "SELECT status, COUNT(*) as jumlah 
                                         FROM presensi 
                                         WHERE user_id = '{$karyawan['id']}' 
                                         AND MONTH(tanggal) = '$bulan' 
                                         AND YEAR(tanggal) = '$tahun'
                                         GROUP BY status";
                                $result = mysqli_query($conn, $query);
                                
                                while ($row = mysqli_fetch_assoc($result)) {
                                    switch ($row['status']) {
                                        case 'Tepat Waktu':
                                            $hadir = $row['jumlah'];
                                            break;
                                        case 'Terlambat':
                                            $terlambat = $row['jumlah'];
                                            break;
                                        case 'Pulang Awal':
                                            $pulang_awal = $row['jumlah'];
                                            break;
                                        case 'Lembur':
                                            $lembur = $row['jumlah'];
                                            break;
                                    }
                                }
                                
                                // Hitung tidak absen (berdasarkan hari kerja per bidang)
                                $tidak_absen = $total_hari_kerja - ($hadir + $terlambat + $pulang_awal + $lembur);
                                if ($tidak_absen < 0) $tidak_absen = 0;
                                
                                // Hitung total denda (Rp 3.000 per hari tidak absen)
                                $total_denda = $tidak_absen * 3000;
                                ?>
                                <tr>
                                    <td><?php echo $karyawan['nik']; ?></td>
                                    <td><?php echo $karyawan['nama']; ?></td>
                                    <td><?php echo $karyawan['nama_bidang']; ?></td>
                                    <td><?php echo $karyawan['jabatan']; ?></td>
                                    <td><?php echo $hadir; ?></td>
                                    <td><?php echo $terlambat; ?></td>
                                    <td><?php echo $pulang_awal; ?></td>
                                    <td><?php echo $lembur; ?></td>
                                    <td><?php echo $tidak_absen; ?></td>
                                    <td><?php echo $total_hari_kerja; ?></td>
                                    <td>Rp <?php echo number_format($total_denda, 0, ',', '.'); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?> 