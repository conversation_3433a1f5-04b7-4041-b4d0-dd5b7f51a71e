<?php
/**
 * Konfigurasi Aplikasi
 * File ini berisi konfigurasi umum untuk aplikasi
 */

// Informasi Aplikasi
define('APP_NAME', 'HadirApp');
define('APP_VERSION', '1.0.0');

// URL Base
$http_host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'localhost';
$base_url = 'http://' . $http_host . '/';
define('BASE_URL', $base_url);

// Path direktori
define('ROOT_PATH', dirname(dirname(__FILE__)) . '/');
define('UPLOAD_PATH', ROOT_PATH . 'uploads/');

// Konfigurasi Upload
define('MAX_UPLOAD_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png']);

// Konfigurasi Session
// Set session cookie parameters
$session_lifetime = 60 * 60 * 24 * 365 * 10; // 10 tahun dalam detik (praktis selamanya)
$secure = false; // Set true jika menggunakan HTTPS
$httponly = true; // Mencegah akses JavaScript ke cookie session

// Cek apakah session sudah dimulai
if (session_status() == PHP_SESSION_NONE) {
    // Set session cookie parameters sebelum session_start()
    session_set_cookie_params($session_lifetime, '/', isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '', $secure, $httponly);

    // Set session garbage collection maksimum lifetime
    @ini_set('session.gc_maxlifetime', $session_lifetime);

    // Start session
    session_start();

    // Selalu perbarui waktu kedaluwarsa session untuk mempertahankan login
    if (isset($_SERVER['HTTP_HOST'])) {
        setcookie(session_name(), session_id(), time() + $session_lifetime, '/', $_SERVER['HTTP_HOST'], $secure, $httponly);
    }
}

// Zona Waktu
date_default_timezone_set('Asia/Jakarta');

// Fungsi untuk redirect
function redirect($url) {
    header("Location: " . BASE_URL . $url);
    exit();
}

// Fungsi untuk menampilkan pesan
function setMessage($type, $message) {
    $_SESSION['message'] = [
        'type' => $type,
        'text' => $message
    ];
}

// Fungsi untuk mendapatkan pesan
function getMessage() {
    if (isset($_SESSION['message'])) {
        $message = $_SESSION['message'];
        unset($_SESSION['message']);
        return $message;
    }
    return null;
}

// Fungsi untuk cek login
function isLoggedIn() {
    // Cek apakah user_id ada di session
    if (!isset($_SESSION['user_id'])) {
        return false;
    }

    // Jika user adalah admin, tidak perlu cek token sesi
    if (isset($_SESSION['role']) && $_SESSION['role'] == 'admin') {
        return true;
    }

    // Cek apakah token sesi valid (hanya untuk karyawan)
    if (isset($_SESSION['session_token'])) {
        global $conn;
        $user_id = $_SESSION['user_id'];
        $session_token = $_SESSION['session_token'];
        $device_id = $_SESSION['device_id'] ?? ($_COOKIE['device_id'] ?? null);

        // Query untuk memeriksa token sesi di tabel user_sessions
        $query = "SELECT * FROM user_sessions WHERE user_id = '$user_id' AND session_token = '$session_token' AND device_id = '$device_id' AND active = 1";
        $result = mysqli_query($conn, $query);

        // Jika token sesi tidak valid, logout
        if (!$result || mysqli_num_rows($result) == 0) {
            // Hapus session
            session_unset();
            session_destroy();

            // Set pesan error
            setMessage('warning', 'Sesi Anda telah berakhir. Silakan login kembali.');

            return false;
        }

        // Update last_activity
        $query = "UPDATE user_sessions SET last_activity = NOW() WHERE user_id = '$user_id' AND session_token = '$session_token' AND active = 1";
        mysqli_query($conn, $query);
    }

    return true;
}

// Fungsi untuk cek role
function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] == 'admin';
}

// Fungsi untuk cek akses
function checkAccess($role) {
    if (!isLoggedIn()) {
        redirect('index.php');
    }

    if ($role == 'admin' && !isAdmin()) {
        redirect('karyawan/index.php');
    }

    if ($role == 'karyawan' && isAdmin()) {
        redirect('admin/index.php');
    }
}
