<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('karyawan');

// Ambil data karyawan
$user_id = $_SESSION['user_id'];
$karyawan = getKaryawanById($user_id);

// Cek apakah data karyawan ditemukan
if (!$karyawan) {
    $_SESSION['error_message'] = 'Data karyawan tidak ditemukan. Silakan hubungi administrator.';
    redirect('logout.php');
}

// Cek perizinan absensi
$query = "SELECT allow_barcode, allow_face, allow_flexible_schedule FROM users WHERE id = '$user_id'";
$result = mysqli_query($conn, $query);
$permissions = mysqli_fetch_assoc($result);

$allow_barcode = isset($permissions['allow_barcode']) && $permissions['allow_barcode'] == 1;
$allow_face = isset($permissions['allow_face']) && $permissions['allow_face'] == 1;
$allow_flexible_schedule = isset($permissions['allow_flexible_schedule']) && $permissions['allow_flexible_schedule'] == 1;

// Ambil data presensi hari ini
$today = date('Y-m-d');
$query = "SELECT * FROM presensi WHERE user_id = '$user_id' AND tanggal = '$today'";
$result = mysqli_query($conn, $query);
$presensi_hari_ini = mysqli_fetch_assoc($result);

// Logika untuk jam kerja fleksibel
$selected_schedule = null;
$jam_kerja_options = [];
$jam_kerja = null;

if ($allow_flexible_schedule) {
    // Cek apakah karyawan sudah memilih jam kerja untuk hari ini
    $query = "SELECT usc.*, jk.*
              FROM user_schedule_choices usc
              JOIN jam_kerja jk ON usc.jam_kerja_id = jk.id
              WHERE usc.user_id = '$user_id' AND usc.tanggal = '$today'";
    $result = mysqli_query($conn, $query);
    if ($result && mysqli_num_rows($result) > 0) {
        $selected_schedule = mysqli_fetch_assoc($result);
        $jam_kerja = $selected_schedule; // Set jam kerja dari jadwal yang dipilih
    }

    // Ambil semua jam kerja yang tersedia untuk bidang ini
    if ($karyawan['bidang_id']) {
        $query = "SELECT DISTINCT jk.*
                  FROM jam_kerja jk
                  JOIN jam_kerja_bidang jkb ON jk.id = jkb.jam_kerja_id
                  WHERE jkb.bidang_id = '{$karyawan['bidang_id']}'";
        $result = mysqli_query($conn, $query);
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $jam_kerja_options[] = $row;
            }
        }
    }
}

// Cek apakah hari ini adalah hari libur
$hari_libur = cekHariLibur($today);

// Ambil data presensi bulan ini
$bulan = date('m');
$tahun = date('Y');
$query = "SELECT COUNT(*) as total FROM presensi
          WHERE user_id = '$user_id'
          AND MONTH(tanggal) = '$bulan'
          AND YEAR(tanggal) = '$tahun'";
$result = mysqli_query($conn, $query);
$row = mysqli_fetch_assoc($result);
$total_presensi_bulan_ini = $row['total'] ?? 0;

// Ambil data keterlambatan bulan ini
$query = "SELECT COUNT(*) as total FROM presensi
          WHERE user_id = '$user_id'
          AND status = 'Terlambat'
          AND MONTH(tanggal) = '$bulan'
          AND YEAR(tanggal) = '$tahun'";
$result = mysqli_query($conn, $query);
$row = mysqli_fetch_assoc($result);
$total_terlambat_bulan_ini = $row['total'] ?? 0;

// Ambil hari kerja untuk bidang karyawan yang login
$working_days_config = [];
if (isset($karyawan['bidang_id'])) {
    try {
        $query_hari_kerja = "SELECT hari, status FROM hari_kerja WHERE bidang_id = '{$karyawan['bidang_id']}'";
        $result_hari_kerja = mysqli_query($conn, $query_hari_kerja);
        if ($result_hari_kerja) {
            while ($row = mysqli_fetch_assoc($result_hari_kerja)) {
                $working_days_config[$row['hari']] = $row['status'];
            }
        }
    } catch (Exception $e) {
        error_log("Error fetching hari_kerja: " . $e->getMessage());
        // Lanjutkan dengan array kosong jika ada error
    }
}

// Ambil daftar hari libur untuk bulan ini
$holiday_dates = [];
try {
    $query_hari_libur = "SELECT tanggal FROM hari_libur WHERE MONTH(tanggal) = '$bulan' AND YEAR(tanggal) = '$tahun'";
    $result_hari_libur = mysqli_query($conn, $query_hari_libur);
    if ($result_hari_libur) {
        while ($row = mysqli_fetch_assoc($result_hari_libur)) {
            $holiday_dates[] = $row['tanggal'];
        }
    }
} catch (Exception $e) {
    error_log("Error fetching hari_libur: " . $e->getMessage());
    // Lanjutkan dengan array kosong jika ada error
}

// Hitung jumlah hari kerja efektif yang sudah berlalu sampai hari ini
$effective_working_days_passed = 0;
$current_day_of_month = (int)date('d');

for ($day = 1; $day <= $current_day_of_month; $day++) {
    $current_date_loop = sprintf('%s-%s-%02d', $tahun, $bulan, $day);
    $day_name = date('l', strtotime($current_date_loop)); // e.g., 'Monday', 'Tuesday'

    // Translate day name to Indonesian for $working_days_config key
    $day_name_indo = '';
    switch ($day_name) {
        case 'Monday': $day_name_indo = 'Senin'; break;
        case 'Tuesday': $day_name_indo = 'Selasa'; break;
        case 'Wednesday': $day_name_indo = 'Rabu'; break;
        case 'Thursday': $day_name_indo = 'Kamis'; break;
        case 'Friday': $day_name_indo = 'Jumat'; break;
        case 'Saturday': $day_name_indo = 'Sabtu'; break;
        case 'Sunday': $day_name_indo = 'Minggu'; break;
    }

    // Cek apakah hari ini adalah hari kerja berdasarkan konfigurasi bidang
    $is_working_day = (isset($working_days_config[$day_name_indo]) && $working_days_config[$day_name_indo] == 1);

    // Cek apakah hari ini bukan hari libur nasional
    $is_holiday = in_array($current_date_loop, $holiday_dates);

    if ($is_working_day && !$is_holiday) {
        $effective_working_days_passed++;
    }
}

// Hitung jumlah presensi sampai hari ini (sesuai hari kerja efektif)
$total_presensi_hari_ini_efektif = 0;
try {
    $query_total_presensi_passed = "SELECT COUNT(*) as total FROM presensi 
                                    WHERE user_id = '$user_id' 
                                    AND MONTH(tanggal) = '$bulan' 
                                    AND YEAR(tanggal) = '$tahun'
                                    AND DAY(tanggal) <= '$current_day_of_month'";
    $result_total_presensi_passed = mysqli_query($conn, $query_total_presensi_passed);
    $row_total_presensi_passed = mysqli_fetch_assoc($result_total_presensi_passed);
    $total_presensi_hari_ini_efektif = $row_total_presensi_passed['total'] ?? 0;
} catch (Exception $e) {
    error_log("Error fetching total_presensi_hari_ini_efektif: " . $e->getMessage());
    $total_presensi_hari_ini_efektif = 0; // Set to 0 on error
}

// Calculate tidak absen/alpa based on effective working days and actual presences
$tidak_absen_efektif = $effective_working_days_passed - $total_presensi_hari_ini_efektif;

// Ensure tidak_absen_efektif is not negative
if ($tidak_absen_efektif < 0) {
    $tidak_absen_efektif = 0;
}

// Ambil data jam kerja
$hari_names = ['', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];
$hari_num = date('N'); // 1 (Senin) sampai 7 (Minggu)
$hari_name = $hari_names[$hari_num];

if ($karyawan['bidang_id']) {
    // Jika karyawan diizinkan memilih jam kerja fleksibel dan sudah memilih jadwal
    if ($allow_flexible_schedule && $selected_schedule) {
        $jam_kerja = $selected_schedule;
    } else if (!$allow_flexible_schedule) {
        // Gunakan jam kerja default berdasarkan hari
        $query = "SELECT jk.*
                  FROM jam_kerja_bidang jkb
                  JOIN jam_kerja jk ON jkb.jam_kerja_id = jk.id
                  WHERE jkb.bidang_id = '{$karyawan['bidang_id']}'
                  AND jkb.hari = '$hari_name'";
        $result = mysqli_query($conn, $query);
        if ($result && mysqli_num_rows($result) > 0) {
            $jam_kerja = mysqli_fetch_assoc($result);
        }
    }
}

// Ambil data izin dinas yang aktif
$today = date('Y-m-d');
$query = "SELECT * FROM izin_dinas
          WHERE user_id = '$user_id'
          AND status = 'Approved'
          AND tanggal_mulai <= '$today'
          AND tanggal_selesai >= '$today'";
$result = mysqli_query($conn, $query);
$izin_dinas_aktif = mysqli_fetch_assoc($result);

// Cek apakah karyawan memiliki jadwal rapat hari ini
$has_rapat_today = false;
$rapat_list_today = [];
try {
    $has_rapat_today = hasRapatToday($user_id);
    $rapat_list_today = getRapatTodayForUser($user_id);
} catch (Exception $e) {
    // Jika terjadi error, set nilai default
    error_log("Error checking rapat: " . $e->getMessage());
}

// Ambil notifikasi yang disetujui
$approved_notifications = [];
try {
    $approved_notifications = getApprovedNotifications($user_id);
} catch (Exception $e) {
    // Jika terjadi error, set nilai default
    error_log("Error getting notifications: " . $e->getMessage());
}

// Cek apakah user adalah approver (NIK 0001)
$is_approver = ($karyawan['nik'] === '0001');
$pending_requests = [];
if ($is_approver) {
    try {
        $pending_requests = getPendingRequestsForApprover();
    } catch (Exception $e) {
        // Jika terjadi error, set nilai default
        error_log("Error getting pending requests: " . $e->getMessage());
    }
}

// Hitung total notifikasi
$total_notifications = count($approved_notifications);
if ($is_approver) {
    $total_notifications += count($pending_requests);
}

// Update status pengumuman yang sudah kadaluarsa
$today = date('Y-m-d');
$query = "UPDATE pengumuman 
          SET status = 'kadaluarsa' 
          WHERE tanggal_kadaluarsa < '$today' 
          AND status = 'aktif'";
mysqli_query($conn, $query);

// Ambil pengumuman yang aktif
$query = "SELECT * FROM pengumuman 
          WHERE status = 'aktif' 
          AND tanggal <= '$today' 
          AND tanggal_kadaluarsa >= '$today'
          ORDER BY tanggal DESC";
$result = mysqli_query($conn, $query);
$pengumuman_list = [];
while ($row = mysqli_fetch_assoc($result)) {
    $pengumuman_list[] = $row;
}

// Include header
include_once '../includes/header.php';
?>

<div class="mobile-app-container">
    <!-- Header Section -->
    <div class="mobile-app-header">
        <div class="header-top">
            <div class="user-info">
                <div class="user-avatar">
                     <?php if (!empty($karyawan['foto_profil'])): ?>
                    <img src="<?php echo BASE_URL . 'uploads/' . $karyawan['foto_profil']; ?>" alt="Foto Profil" style="width: 100px; height: 100px; ">
                <?php else: ?>
                    <i class="fas fa-user"></i>
                <?php endif; ?>
                </div>
                <div class="user-details">
                    <div class="user-name"><?php echo $_SESSION['nama']; ?></div>
                    <div class="user-position"><?php echo $karyawan['bidang'] ?? 'Karyawan'; ?></div>
                </div>
            </div>
            <!-- Notification Bell Icon (menggabungkan kedua jenis notifikasi) -->
            <a href="#" class="notification-button <?php echo ($is_approver && count($pending_requests) > 0) ? 'pending-approver-button' : ''; ?>" id="notificationBell" title="Notifikasi">
                <i class="fas fa-bell text-white"></i>
                <?php if ($total_notifications > 0): ?>
                    <span class="badge bg-danger rounded-pill notification-count total-count"><?php echo $total_notifications; ?></span>
                <?php endif; ?>
            </a>
        </div>

        <div class="header-bottom">
            <div class="work-schedule-info">
                <?php if ($allow_flexible_schedule && $selected_schedule): ?>
                    <div class="schedule-info flexible">
                        <i class="fas fa-clock"></i>
                        <span><?php echo $selected_schedule['nama_jam_kerja']; ?> (<?php echo $jam_kerja['jam_masuk']; ?> - <?php echo $jam_kerja['jam_pulang']; ?>)</span>
                        <i class="fas fa-check-circle status-icon"></i>
                    </div>
                <?php elseif ($allow_flexible_schedule && !$selected_schedule): ?>
                    <div class="schedule-info pending">
                        <i class="fas fa-clock"></i>
                        <span>Jam Kerja Fleksibel - Belum dipilih</span>
                        <i class="fas fa-exclamation-circle status-icon"></i>
                    </div>
                <?php elseif ($jam_kerja): ?>
                    <div class="schedule-info default">
                        <i class="fas fa-clock"></i>
                        <span><?php echo $jam_kerja['nama_jam_kerja'] ?? 'Jam Kerja'; ?> (<?php echo $jam_kerja['jam_masuk']; ?> - <?php echo $jam_kerja['jam_pulang']; ?>)</span>
                    </div>
                <?php else: ?>
                    <div class="schedule-info no-schedule">
                        <i class="fas fa-clock"></i>
                        <span>Jam Kerja - Belum diatur</span>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Status Section -->
    <div class="container px-0">
        <?php if ($izin_dinas_aktif): ?>
        <div class="alert alert-info mb-3">
            <i class="fas fa-plane me-2"></i> Anda sedang dalam perjalanan dinas ke <strong><?php echo $izin_dinas_aktif['tujuan']; ?></strong> sampai tanggal <?php echo date('d/m/Y', strtotime($izin_dinas_aktif['tanggal_selesai'])); ?>
        </div>
        <?php endif; ?>

        <!-- Status Card -->
        <div class="status-card">
            <div class="status-title">Status Absensi Hari Ini</div>
            <div class="status-content">
                <div class="status-value">
                    <?php if ($hari_libur): ?>
                        <span class="status-badge danger">Hari Libur</span>
                        <div class="holiday-name"><?php echo $hari_libur['nama_libur']; ?></div>
                    <?php elseif (empty($presensi_hari_ini)): ?>
                        <span class="status-badge warning">Belum Absen</span>
                    <?php elseif (empty($presensi_hari_ini['jam_pulang'])): ?>
                        <span class="status-badge info">Sudah Absen Masuk</span>
                    <?php else: ?>
                        <span class="status-badge success">Sudah Absen Lengkap</span>
                    <?php endif; ?>
                </div>
                <div class="status-icon <?php echo $hari_libur ? 'danger' : 'primary'; ?>">
                    <i class="<?php echo $hari_libur ? 'fas fa-calendar-times' : 'fas fa-calendar-check'; ?>"></i>
                </div>
            </div>
        </div>

        
        <!-- Quick Actions -->
        <div class="section-header">
            <h6 class="mb-3">Menu Cepat</h6>
            <div class="d-flex align-items-center">
                <span class="me-2 text-primary" id="menuLainnyaText" style="font-size: 14px; font-weight: 500; cursor: pointer; user-select: none;">Menu Lainnya</span>
                <button type="button" class="toggle-arrow-button" id="toggleAdditionalMenu" style="background: none; border: none; color: #4e73df; font-size: 16px; cursor: pointer; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease; background-color: rgba(78, 115, 223, 0.1);">
                    <i class="fas fa-chevron-down" id="toggleIcon" style="transition: transform 0.3s ease;"></i>
                </button>
            </div>
        </div>
        <div class="quick-actions">
            <?php if ($izin_dinas_aktif): ?>
            <a href="javascript:void(0)" class="action-button" onclick="showDinasAlert()">
                <div class="action-icon" style="background-color: #adb5bd;">
                    <i class="fas fa-camera"></i>
                </div>
                <div class="action-text" style="color: #6c757d;">Absensi</div>
            </a>
            <?php elseif ($hari_libur): ?>
            <a href="javascript:void(0)" class="action-button" onclick="showHolidayAlert()">
                <div class="action-icon danger">
                    <i class="fas fa-calendar-times"></i>
                </div>
                <div class="action-text" style="color: #721c24;">Absensi</div>
            </a>
            <?php elseif (!$allow_face && !$allow_barcode): ?>
            <a href="javascript:void(0)" class="action-button" onclick="showNoPermissionAlert()">
                <div class="action-icon" style="background-color: #adb5bd;">
                    <i class="fas fa-ban"></i>
                </div>
                <div class="action-text" style="color: #6c757d;">Absensi</div>
            </a>
            <?php else: ?>
            <?php if ($allow_face && $allow_barcode): ?>
            <!-- Jika diizinkan keduanya, arahkan ke halaman presensi -->
            <a href="presensi.php" class="action-button">
                <div class="action-icon primary">
                    <i class="fas fa-camera"></i>
                </div>
                <div class="action-text">Absensi</div>
            </a>
            <?php elseif ($allow_face): ?>
            <!-- Jika hanya diizinkan wajah, arahkan ke halaman presensi -->
            <a href="presensi.php" class="action-button">
                <div class="action-icon primary">
                    <i class="fas fa-camera"></i>
                </div>
                <div class="action-text">Absensi Wajah</div>
            </a>
            <?php elseif ($allow_barcode): ?>
            <!-- Jika hanya diizinkan barcode, arahkan langsung ke halaman scan barcode -->
            <a href="scan_barcode.php?type=<?php echo empty($presensi_hari_ini) ? 'masuk' : (empty($presensi_hari_ini['jam_pulang']) ? 'pulang' : 'masuk'); ?>" class="action-button">
                <div class="action-icon primary">
                    <i class="fas fa-qrcode"></i>
                </div>
                <div class="action-text">Scan Barcode</div>
            </a>
            <?php endif; ?>
            <?php endif; ?>
            <a href="riwayat.php" class="action-button">
                <div class="action-icon success">
                    <i class="fas fa-history"></i>
                </div>
                <div class="action-text">Riwayat</div>
            </a>
            <?php if ($has_rapat_today): ?>
            <a href="scan_rapat.php" class="action-button">
                <div class="action-icon primary">
                    <i class="fas fa-users"></i>
                </div>
                <div class="action-text">Scan Rapat</div>
            </a>
            <?php else: ?>
            <a href="javascript:void(0)" class="action-button" onclick="showNoRapatAlert()">
                <div class="action-icon" style="background-color: #adb5bd;">
                    <i class="fas fa-users"></i>
                </div>
                <div class="action-text" style="color: #6c757d;">Scan Rapat</div>
            </a>
            <?php endif; ?>
            <a href="izin_dinas.php" class="action-button">
                <div class="action-icon warning">
                    <i class="fas fa-plane"></i>
                </div>
                <div class="action-text">Izin Dinas</div>
            </a>
        </div>

        <!-- Additional Quick Actions -->
        <div id="additionalMenu" class="additional-menu" style="background-color: rgba(246, 246, 247, 0.95); border-radius: 12px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); margin-bottom: 20px; overflow: hidden; transition: all 0.3s ease; max-height: 0; opacity: 0;">
            <div class="quick-actions">
                <a href="gangguan_absensi.php" class="action-button">
                    <div class="action-icon danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="action-text">Gangguan Absensi</div>
                </a>
                <a href="<?php echo BASE_URL; ?>help.php" class="action-button">
                    <div class="action-icon primary">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="action-text">Bantuan</div>
                </a>
                <a href="<?php echo BASE_URL; ?>change_password.php" class="action-button">
                    <div class="action-icon success">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="action-text">Ubah Password</div>
                </a>
                <?php if ($allow_flexible_schedule && !empty($jam_kerja_options)): ?>
                <a href="#" class="action-button" onclick="showScheduleSelectionModal()">
                    <div class="action-icon primary">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="action-text">Pilih Jam Kerja</div>
                </a>
                <?php else: ?>
                <a href="#" class="action-button" onclick="window.location.reload()">
                    <div class="action-icon warning">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="action-text">Refresh</div>
                </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Modal Pilih Jam Kerja -->
        <div class="modal fade" id="scheduleSelectionModal" tabindex="-1" aria-labelledby="scheduleSelectionModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="scheduleSelectionModalLabel">Pilih Jam Kerja</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="jam_kerja_id" class="form-label">Pilih Jadwal Kerja Hari Ini:</label>
                                <select class="form-select" id="jam_kerja_id" name="jam_kerja_id" required>
                                    <?php foreach ($jam_kerja_options as $option): ?>
                                    <option value="<?php echo $option['id']; ?>">
                                        <?php echo $option['nama_jam_kerja']; ?> (<?php echo $option['jam_masuk']; ?> - <?php echo $option['jam_pulang']; ?>)
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="d-grid">
                                <button type="submit" name="pilih_jam_kerja" class="btn btn-primary">Simpan Pilihan</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
<!-- Pengumuman Section -->
<?php
        if (!empty($pengumuman_list)):
        ?>
        <div class="info-section mt-3">
            <div class="info-title">
                <i class="fas fa-bullhorn me-2"></i> Pengumuman
            </div>
            <div class="info-content">
                <div class="swiper pengumuman-swiper">
                    <div class="swiper-wrapper">
                        <?php foreach ($pengumuman_list as $pengumuman): ?>
                        <div class="swiper-slide">
                            <div class="pengumuman-item">
                                <?php if ($pengumuman['foto']): ?>
                                <div class="pengumuman-image-container">
                                    <img src="<?php echo BASE_URL . 'uploads/pengumuman/' . $pengumuman['foto']; ?>" 
                                         alt="Foto Pengumuman" 
                                         class="pengumuman-image">
                                </div>
                                <?php endif; ?>
                                <div class="pengumuman-info">
                                    <h6 class="pengumuman-title"><?php echo $pengumuman['judul']; ?></h6>
                                    <p class="pengumuman-date">
                                        <i class="fas fa-calendar-alt"></i> <?php echo date('d/m/Y', strtotime($pengumuman['tanggal'])); ?> - 
                                        <i class="fas fa-clock"></i> <?php echo date('d/m/Y', strtotime($pengumuman['tanggal_kadaluarsa'])); ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="swiper-pagination"></div>
                    <div class="swiper-button-prev"></div>
                    <div class="swiper-button-next"></div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Grafik Absensi Section -->
        <div class="info-section mt-3">
            <div class="info-title">
                <i class="fas fa-chart-bar me-2"></i> Statistik Absensi Bulan Ini
            </div>
            <div class="info-content">
                <canvas id="absensiChart" style="width: 100%; height: 300px;"></canvas>
            </div>
        </div>

        <!-- Info Section -->
        <div class="info-section">
            <div class="info-title d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="fas fa-info-circle me-2"></i> Informasi Absensi
                </div>
                <div class="d-flex align-items-center">
                    <span class="me-2 text-primary" id="infoToggleText" style="font-size: 14px; font-weight: 500; cursor: pointer; user-select: none;">Tampilkan Info</span>
                    <button type="button" class="toggle-arrow-button" id="toggleInfoButton" style="background: none; border: none; color: #4e73df; font-size: 16px; cursor: pointer; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease; background-color: rgba(78, 115, 223, 0.1);">
                        <i class="fas fa-chevron-down" id="toggleInfoIcon" style="transition: transform 0.3s ease;"></i>
                    </button>
                </div>
            </div>
            <div class="info-content hidden" id="infoContent">
                <ol>
                    <li>Absensi masuk dapat dilakukan mulai pukul <?php echo $jam_kerja ? $jam_kerja['awal_jam_masuk'] : '07:30'; ?></li>
                    <li>Batas waktu absensi masuk adalah pukul <?php echo $jam_kerja ? $jam_kerja['jam_masuk'] : '08:00'; ?></li>
                    <li>Absensi pulang dapat dilakukan mulai pukul <?php echo $jam_kerja ? $jam_kerja['jam_pulang'] : '17:00'; ?></li>
                    <li>Absensi harus dilakukan di lokasi yang telah ditentukan</li>
                    <li>Pastikan kamera dan GPS perangkat Anda aktif saat melakukan absensi</li>
                </ol>
            </div>
        </div>

        <?php if (!empty($rapat_list_today)): ?>
        <!-- Jadwal Rapat Section -->
        <div class="info-section mt-3">
            <div class="info-title">
                <i class="fas fa-users"></i> Jadwal Rapat Hari Ini
                <?php
                $belum_hadir = 0;
                foreach ($rapat_list_today as $rapat) {
                    if ($rapat['status'] != 'hadir') {
                        $belum_hadir++;
                    }
                }
                if ($belum_hadir > 0):
                ?>
                <span class="badge bg-warning text-dark" style="font-size: 10px; vertical-align: middle;"><?php echo $belum_hadir; ?> belum hadir</span>
                <?php endif; ?>
            </div>
            <div class="info-content p-0">
                <div class="list-group list-group-flush">
                    <?php foreach ($rapat_list_today as $rapat): ?>
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1"><?php echo $rapat['judul']; ?></h6>
                                <p class="mb-1 small"><i class="fas fa-map-marker-alt"></i> <?php echo $rapat['lokasi']; ?></p>
                                <p class="mb-1 small"><i class="fas fa-clock"></i> <?php echo date('H:i', strtotime($rapat['waktu_mulai'])) . ' - ' . date('H:i', strtotime($rapat['waktu_selesai'])); ?></p>
                            </div>
                            <div>
                                <?php if ($rapat['status'] == 'hadir'): ?>
                                <span class="badge bg-success">Sudah Hadir</span>
                                <?php else: ?>
                                <span class="badge bg-warning text-dark">Belum Hadir</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Floating Attendance Button -->
        <?php if ($izin_dinas_aktif): ?>
        <a href="javascript:void(0)" class="attendance-button" style="background: linear-gradient(135deg, #adb5bd 0%, #6c757d 100%);" onclick="showDinasAlert()" title="Anda sedang dalam perjalanan dinas">
            <i class="fas fa-camera"></i>
        </a>
        <?php elseif ($hari_libur): ?>
        <a href="javascript:void(0)" class="attendance-button" style="background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);" onclick="showHolidayAlert()" title="Hari ini adalah hari libur">
            <i class="fas fa-calendar-times"></i>
        </a>
        <?php elseif (!$allow_face && !$allow_barcode): ?>
        <a href="javascript:void(0)" class="attendance-button" style="background: linear-gradient(135deg, #adb5bd 0%, #6c757d 100%);" onclick="showNoPermissionAlert()" title="Anda tidak memiliki izin untuk melakukan absensi">
            <i class="fas fa-ban"></i>
        </a>
        <?php else: ?>
            <?php if ($allow_face && $allow_barcode): ?>
            <!-- Jika diizinkan keduanya, arahkan ke halaman presensi -->
            <a href="presensi.php" class="attendance-button" title="Mulai Absensi">
                <i class="fas fa-camera"></i>
            </a>
            <?php elseif ($allow_face): ?>
            <!-- Jika hanya diizinkan wajah, arahkan ke halaman presensi -->
            <a href="presensi.php" class="attendance-button" title="Mulai Absensi Wajah">
                <i class="fas fa-camera"></i>
            </a>
            <?php elseif ($allow_barcode): ?>
            <!-- Jika hanya diizinkan barcode, arahkan langsung ke halaman scan barcode -->
            <a href="scan_barcode.php?type=<?php echo empty($presensi_hari_ini) ? 'masuk' : (empty($presensi_hari_ini['jam_pulang']) ? 'pulang' : 'masuk'); ?>" class="attendance-button" title="Scan Barcode">
                <i class="fas fa-qrcode"></i>
            </a>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>

<!-- Script untuk menampilkan SweetAlert ketika absen berhasil -->
<?php if (isset($_GET['absen_status']) && $_GET['absen_status'] == 'success'): ?>
<script>
    // Fungsi untuk memastikan SweetAlert2 dimuat
    function ensureSweetAlertLoaded(callback, maxAttempts = 10, interval = 300) {
        let attempts = 0;

        function checkSwal() {
            attempts++;
            if (typeof Swal !== 'undefined') {
                callback();
            } else if (attempts < maxAttempts) {
                console.log('SweetAlert2 belum dimuat, mencoba lagi... (percobaan ' + attempts + ')');
                setTimeout(checkSwal, interval);
            } else {
                console.error('SweetAlert2 tidak dapat dimuat setelah ' + maxAttempts + ' percobaan');
                // Fallback ke alert biasa
                var absenType = '<?php echo isset($_GET['type']) ? $_GET['type'] : 'masuk'; ?>';
                alert('Absensi ' + (absenType === 'masuk' ? 'Masuk' : 'Pulang') + ' Berhasil\n\nData absensi ' +
                      (absenType === 'masuk' ? 'masuk' : 'pulang') + ' Anda telah berhasil disimpan.');
            }
        }

        checkSwal();
    }

    // Jalankan ketika dokumen dimuat
    document.addEventListener('DOMContentLoaded', function() {
        ensureSweetAlertLoaded(function() {
            // Tentukan tipe absen (masuk atau pulang)
            var absenType = '<?php echo isset($_GET['type']) ? $_GET['type'] : 'masuk'; ?>';

            // Gunakan pesan dari parameter URL jika ada
            var message = '<?php echo isset($_GET['message']) ? htmlspecialchars(urldecode($_GET['message'])) : ''; ?>';
            var title = 'Absensi ' + (absenType === 'masuk' ? 'Masuk' : 'Pulang') + ' Berhasil';
            var text = message ? message : 'Data absensi ' + (absenType === 'masuk' ? 'masuk' : 'pulang') + ' Anda telah berhasil disimpan.';

            // Tampilkan SweetAlert
            Swal.fire({
                icon: 'success',
                title: title,
                text: text,
                timer: 3000,
                timerProgressBar: true,
                showConfirmButton: false
            });
        });
    });
</script>
<?php endif; ?>

<!-- Script untuk menampilkan SweetAlert ketika scan rapat berhasil -->
<?php if (isset($_GET['rapat_status']) && $_GET['rapat_status'] == 'success'): ?>
<script>
    // Jalankan ketika dokumen dimuat
    document.addEventListener('DOMContentLoaded', function() {
        // Fungsi untuk memastikan SweetAlert2 dimuat
        function ensureSweetAlertLoaded(callback, maxAttempts = 10, interval = 300) {
            let attempts = 0;

            function checkSwal() {
                attempts++;
                if (typeof Swal !== 'undefined') {
                    callback();
                } else if (attempts < maxAttempts) {
                    console.log('SweetAlert2 belum dimuat, mencoba lagi... (percobaan ' + attempts + ')');
                    setTimeout(checkSwal, interval);
                } else {
                    console.error('SweetAlert2 tidak dapat dimuat setelah ' + maxAttempts + ' percobaan');
                    // Fallback ke alert biasa
                    alert('Absensi Rapat Berhasil\n\nKehadiran Anda dalam rapat telah berhasil dicatat.');
                }
            }

            checkSwal();
        }

        ensureSweetAlertLoaded(function() {
            // Cek apakah ada pesan di sessionStorage
            var rapatSuccess = sessionStorage.getItem('rapatSuccess');
            var rapatMessage = sessionStorage.getItem('rapatMessage');

            if (rapatSuccess) {
                // Tampilkan SweetAlert
                Swal.fire({
                    icon: 'success',
                    title: 'Absensi Rapat Berhasil',
                    html: rapatMessage || 'Kehadiran Anda dalam rapat telah berhasil dicatat.',
                    timer: 3000,
                    timerProgressBar: true,
                    showConfirmButton: false
                });

                // Hapus data dari sessionStorage
                sessionStorage.removeItem('rapatSuccess');
                sessionStorage.removeItem('rapatMessage');
            }
        });
    });
</script>
<?php endif; ?>

<!-- Script untuk menampilkan SweetAlert ketika karyawan memiliki jadwal rapat hari ini yang belum dihadiri -->
<?php
// Hitung jumlah rapat yang belum dihadiri
$belum_hadir_count = 0;
if (!empty($rapat_list_today)) {
    foreach ($rapat_list_today as $rapat) {
        if ($rapat['status'] != 'hadir') {
            $belum_hadir_count++;
        }
    }
}

// Tampilkan popup hanya jika ada rapat yang belum dihadiri
if ($belum_hadir_count > 0):
?>
<script>
    // Jalankan ketika dokumen dimuat
    document.addEventListener('DOMContentLoaded', function() {
        // Fungsi untuk memastikan SweetAlert2 dimuat
        function ensureSweetAlertLoaded(callback, maxAttempts = 10, interval = 300) {
            let attempts = 0;

            function checkSwal() {
                attempts++;
                if (typeof Swal !== 'undefined') {
                    callback();
                } else if (attempts < maxAttempts) {
                    console.log('SweetAlert2 belum dimuat, mencoba lagi... (percobaan ' + attempts + ')');
                    setTimeout(checkSwal, interval);
                } else {
                    console.error('SweetAlert2 tidak dapat dimuat setelah ' + maxAttempts + ' percobaan');
                    // Fallback ke alert biasa
                    alert('Anda memiliki jadwal rapat hari ini yang belum dihadiri. Silakan cek jadwal rapat Anda.');
                }
            }

            checkSwal();
        }

        ensureSweetAlertLoaded(function() {
            // Buat daftar rapat yang belum dihadiri dalam format HTML
            var rapatListHtml = '<div class="text-left"><ul class="list-group">';
            <?php foreach ($rapat_list_today as $rapat): ?>
                <?php if ($rapat['status'] != 'hadir'): ?>
                rapatListHtml += '<li class="list-group-item">' +
                    '<strong><?php echo htmlspecialchars($rapat['judul']); ?></strong><br>' +
                    '<i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($rapat['lokasi']); ?><br>' +
                    '<i class="fas fa-clock"></i> <?php echo date('H:i', strtotime($rapat['waktu_mulai'])) . ' - ' . date('H:i', strtotime($rapat['waktu_selesai'])); ?><br>' +
                    '<span class="badge bg-warning text-dark">Belum Hadir</span>' +
                    '</li>';
                <?php endif; ?>
            <?php endforeach; ?>
            rapatListHtml += '</ul></div>';

            // Tampilkan SweetAlert
            Swal.fire({
                icon: 'info',
                title: 'Jadwal Rapat Hari Ini',
                html: rapatListHtml,
                confirmButtonColor: '#3085d6',
                confirmButtonText: 'Saya Mengerti'
            });
        });
    });
</script>
<?php endif; ?>

<?php if ($izin_dinas_aktif): ?>
<script>
    function showDinasAlert() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'info',
                title: 'Perjalanan Dinas Aktif',
                text: 'Anda sedang dalam perjalanan dinas ke <?php echo $izin_dinas_aktif['tujuan']; ?>. Presensi Anda akan diisi otomatis selama periode perjalanan dinas.',
                confirmButtonColor: '#3085d6',
                confirmButtonText: 'Saya Mengerti'
            });
        } else {
            alert('Anda sedang dalam perjalanan dinas. Presensi diisi otomatis.');
        }
    }
</script>
<?php endif; ?>

<?php if ($hari_libur): ?>
<script>
    function showHolidayAlert() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'warning',
                title: 'Hari Libur',
                text: 'Hari ini adalah hari libur: <?php echo $hari_libur['nama_libur']; ?>. Tidak perlu melakukan absensi pada hari libur.',
                confirmButtonColor: '#e74a3b',
                confirmButtonText: 'Saya Mengerti'
            });
        } else {
            alert('Hari ini adalah hari libur: <?php echo $hari_libur['nama_libur']; ?>. Tidak perlu melakukan absensi.');
        }
    }
</script>
<?php endif; ?>

<!-- Script untuk menampilkan pesan ketika tidak memiliki izin absensi -->
<script>
    function showNoPermissionAlert() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'warning',
                title: 'Tidak Ada Izin Absensi',
                text: 'Anda tidak memiliki izin untuk melakukan absensi. Silakan hubungi administrator untuk mendapatkan izin absensi.',
                confirmButtonColor: '#6c757d',
                confirmButtonText: 'Saya Mengerti'
            });
        } else {
            alert('Anda tidak memiliki izin untuk melakukan absensi. Silakan hubungi administrator.');
        }
    }

    function showNoRapatAlert() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'info',
                title: 'Tidak Ada Jadwal Rapat',
                text: 'Anda tidak memiliki jadwal rapat hari ini atau semua rapat Anda hari ini sudah dihadiri.',
                confirmButtonColor: '#6c757d',
                confirmButtonText: 'Saya Mengerti'
            });
        } else {
            alert('Anda tidak memiliki jadwal rapat hari ini atau semua rapat Anda hari ini sudah dihadiri.');
        }
    }

    function showScheduleSelectionModal() {
        var modal = new bootstrap.Modal(document.getElementById('scheduleSelectionModal'));
        modal.show();
    }

    // Toggle Menu Lainnya
    document.addEventListener('DOMContentLoaded', function() {
        const toggleButton = document.getElementById('toggleAdditionalMenu');
        const additionalMenu = document.getElementById('additionalMenu');
        const toggleIcon = document.getElementById('toggleIcon');
        const menuText = document.getElementById('menuLainnyaText');

        // Tambahkan CSS untuk class show jika belum ada
        if (!document.querySelector('style[data-toggle-menu]')) {
            const style = document.createElement('style');
            style.setAttribute('data-toggle-menu', 'true');
            style.textContent = `
                .additional-menu.show {
                    max-height: 500px !important;
                    opacity: 1 !important;
                }
                .toggle-arrow-button.active i {
                    transform: rotate(180deg) !important;
                }
            `;
            document.head.appendChild(style);
        }

        if (toggleButton && additionalMenu && toggleIcon) {
            // Event listener untuk tombol toggle
            toggleButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const isMenuVisible = additionalMenu.classList.contains('show');

                if (isMenuVisible) {
                    // Sembunyikan menu
                    additionalMenu.classList.remove('show');
                    toggleIcon.classList.remove('fa-chevron-up');
                    toggleIcon.classList.add('fa-chevron-down');
                    toggleButton.classList.remove('active');
                    if (menuText) {
                        menuText.textContent = 'Menu Lainnya';
                    }
                } else {
                    // Tampilkan menu
                    additionalMenu.classList.add('show');
                    toggleIcon.classList.remove('fa-chevron-down');
                    toggleIcon.classList.add('fa-chevron-up');
                    toggleButton.classList.add('active');
                    if (menuText) {
                        menuText.textContent = 'Tutup Menu';
                    }
                }
            });

            // Event listener untuk span text juga
            if (menuText) {
                menuText.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleButton.click();
                });

                // Tambahkan style cursor pointer
                menuText.style.cursor = 'pointer';
            }
        }
    });
</script>

<!-- JavaScript untuk notifikasi -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const notificationBell = document.getElementById('notificationBell');
        const approvedNotifications = <?php echo json_encode($approved_notifications); ?>;
        const isApprover = <?php echo json_encode($is_approver); ?>;
        const pendingRequests = <?php echo json_encode($pending_requests); ?>;

        if (notificationBell) {
            notificationBell.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Hapus badge notifikasi
                const badge = this.querySelector('.notification-count');
                if (badge) {
                    badge.remove();
                }

                let htmlContent = '';
                let hasNotifications = false;

                // Tampilkan notifikasi persetujuan Anda
                if (approvedNotifications.length > 0) {
                    hasNotifications = true;
                    htmlContent += '<h6 class="text-start fw-bold mb-3">Persetujuan untuk Anda:</h6>';
                    approvedNotifications.forEach(notification => {
                        let title = '';
                        let icon = '';
                        if (notification.type === 'gangguan_absensi') {
                            title = 'Gangguan Absensi Disetujui';
                            icon = '<i class="fas fa-exclamation-triangle text-warning me-2"></i>';
                        } else if (notification.type === 'izin_dinas') {
                            title = 'Izin Dinas Disetujui';
                            icon = '<i class="fas fa-plane text-info me-2"></i>';
                        }
                        htmlContent += `
                            <div class="card mb-3 notification-card">
                                <div class="card-body">
                                    <h5 class="card-title">${icon} ${title}</h5>
                                    <p class="card-text mb-1"><strong>Tanggal:</strong> ${notification.date}</p>
                                    <p class="card-text"><strong>Keterangan:</strong> ${notification.keterangan}</p>
                                    <div class="d-flex justify-content-center mt-3">
                                        <button class="btn btn-primary btn-sm mark-read-button" 
                                                data-id="${notification.id}" 
                                                data-type="${notification.type}">
                                            <i class="fas fa-check"></i> Tandai Sudah Dibaca
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    htmlContent += '<hr class="my-4">';
                }

                // Tampilkan pengajuan pending untuk approver
                if (isApprover && pendingRequests.length > 0) {
                    hasNotifications = true;
                    htmlContent += '<h6 class="text-start fw-bold mb-3">Pengajuan Menunggu Persetujuan:</h6>';
                    pendingRequests.forEach(request => {
                        let title = '';
                        let icon = '';
                        if (request.type === 'gangguan_absensi') {
                            title = 'Pengajuan Gangguan Absensi';
                            icon = '<i class="fas fa-question-circle text-secondary me-2"></i>';
                        } else if (request.type === 'izin_dinas') {
                            title = 'Pengajuan Izin Dinas';
                            icon = '<i class="fas fa-clipboard-list text-secondary me-2"></i>';
                        }
                        htmlContent += `
                            <div class="card mb-3 notification-card">
                                <div class="card-body">
                                    <h5 class="card-title">${icon} ${title}</h5>
                                    <p class="card-text mb-1"><strong>Tanggal:</strong> ${request.date}</p>
                                    <p class="card-text mb-1"><strong>Keterangan:</strong> ${request.keterangan}</p>
                                    <p class="card-text"><strong>Diajukan oleh:</strong> ${request.nama_pengaju}</p>
                                    ${request.type === 'gangguan_absensi' ? `
                                        <div class="mt-2">
                                            ${request.foto_wajah ? `
                                                <p class="card-text mb-1"><strong>Foto Wajah:</strong></p>
                                                <a href="<?php echo BASE_URL; ?>view_image.php?type=gangguan_absensi&path=${request.foto_wajah}&title=Foto Wajah - ${request.nama_pengaju}">
                                                    <img src="<?php echo BASE_URL; ?>uploads/gangguan_absensi/${request.foto_wajah}" 
                                                         alt="Foto Wajah" class="img-fluid rounded" style="max-height: 200px;">
                                                </a>
                                            ` : ''}
                                            ${request.bukti_foto ? `
                                                <p class="card-text mb-1"><strong>Bukti Foto:</strong></p>
                                                <a href="<?php echo BASE_URL; ?>view_image.php?type=gangguan_absensi&path=${request.bukti_foto}&title=Bukti Foto - ${request.nama_pengaju}">
                                                    <img src="<?php echo BASE_URL; ?>uploads/gangguan_absensi/${request.bukti_foto}" 
                                                         alt="Bukti Foto" class="img-fluid rounded" style="max-height: 200px;">
                                                </a>
                                            ` : ''}
                                        </div>
                                    ` : ''}
                                    ${request.type === 'izin_dinas' ? `
                                        <div class="mt-2">
                                            ${request.foto_surat_tugas ? `
                                                <p class="card-text mb-1"><strong>Foto Surat Tugas:</strong></p>
                                                <a href="<?php echo BASE_URL; ?>view_image.php?type=izin_dinas&path=${request.foto_surat_tugas}&title=Foto Surat Tugas - ${request.nama_pengaju}">
                                                    <img src="<?php echo BASE_URL; ?>uploads/${request.foto_surat_tugas}" 
                                                         alt="Foto Surat Tugas" class="img-fluid rounded" style="max-height: 200px;">
                                                </a>
                                            ` : ''}
                                            ${request.foto_wajah ? `
                                                <p class="card-text mb-1"><strong>Foto Wajah:</strong></p>
                                                <a href="<?php echo BASE_URL; ?>view_image.php?type=izin_dinas&path=${request.foto_wajah}&title=Foto Wajah - ${request.nama_pengaju}">
                                                    <img src="<?php echo BASE_URL; ?>uploads/${request.foto_wajah}" 
                                                         alt="Foto Wajah" class="img-fluid rounded" style="max-height: 200px;">
                                                </a>
                                            ` : ''}
                                        </div>
                                    ` : ''}
                                    <div class="d-flex justify-content-center gap-2 mt-3">
                                        <button class="btn btn-success btn-sm approve-button" data-id="${request.id}" data-type="${request.type}">Setujui</button>
                                        <button class="btn btn-danger btn-sm reject-button" data-id="${request.id}" data-type="${request.type}">Tolak</button>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                }

                if (hasNotifications) {
                    Swal.fire({
                        title: 'Daftar Notifikasi',
                        icon: 'info',
                        html: htmlContent,
                        showCancelButton: true,
                        confirmButtonText: 'Tutup',
                        customClass: {
                            popup: 'notification-popup',
                            confirmButton: 'swal2-confirm-button',
                            cancelButton: 'swal2-cancel-button'
                        },
                        reverseButtons: true,
                        didOpen: () => {
                            // Event listener untuk tombol setujui
                            const approveButtons = Swal.getHtmlContainer().querySelectorAll('.approve-button');
                            approveButtons.forEach(button => {
                                button.addEventListener('click', function() {
                                    const requestId = this.dataset.id;
                                    const requestType = this.dataset.type;
                                    Swal.close(); // Tutup sweetalert notifikasi utama
                                    Swal.fire({
                                        title: 'Konfirmasi Persetujuan',
                                        text: `Anda yakin ingin menyetujui pengajuan ${requestType} ini?`,
                                        icon: 'question',
                                        showCancelButton: true,
                                        confirmButtonText: 'Ya, Setujui',
                                        cancelButtonText: 'Batal',
                                        reverseButtons: true
                                    }).then((confirmResult) => {
                                        if (confirmResult.isConfirmed) {
                                            fetch('<?php echo BASE_URL; ?>api/approve_request.php', {
                                                method: 'POST',
                                                headers: {
                                                    'Content-Type': 'application/json',
                                                },
                                                body: JSON.stringify({ id: requestId, type: requestType, approver_id: <?php echo $user_id; ?> })
                                            }).then(response => response.json())
                                              .then(data => {
                                                if (data.success) {
                                                    Swal.fire('Berhasil', data.message, 'success').then(() => {
                                                        location.reload();
                                                    });
                                                } else {
                                                    Swal.fire('Error', data.message, 'error');
                                                }
                                            })
                                            .catch(error => {
                                                console.error('Error:', error);
                                                Swal.fire('Error', 'Terjadi kesalahan saat menyetujui pengajuan.', 'error');
                                            });
                                        }
                                    });
                                });
                            });

                            // Event listener untuk tombol tolak
                            const rejectButtons = Swal.getHtmlContainer().querySelectorAll('.reject-button');
                            rejectButtons.forEach(button => {
                                button.addEventListener('click', function() {
                                    const requestId = this.dataset.id;
                                    const requestType = this.dataset.type;
                                    Swal.close(); // Tutup sweetalert notifikasi utama
                                    Swal.fire({
                                        title: 'Konfirmasi Penolakan',
                                        text: `Anda yakin ingin menolak pengajuan ${requestType} ini?`,
                                        icon: 'warning',
                                        showCancelButton: true,
                                        confirmButtonText: 'Ya, Tolak',
                                        cancelButtonText: 'Batal',
                                        reverseButtons: true
                                    }).then((confirmResult) => {
                                        if (confirmResult.isConfirmed) {
                                            fetch('<?php echo BASE_URL; ?>api/mark_notifications.php', {
                                                method: 'POST',
                                                headers: {
                                                    'Content-Type': 'application/json',
                                                },
                                                body: JSON.stringify({ id: requestId, type: requestType, approver_id: <?php echo $user_id; ?> })
                                            }).then(response => response.json())
                                              .then(data => {
                                                if (data.success) {
                                                    Swal.fire('Berhasil', data.message, 'success').then(() => {
                                                        location.reload();
                                                    });
                                                } else {
                                                    Swal.fire('Error', data.message, 'error');
                                                }
                                            })
                                            .catch(error => {
                                                console.error('Error:', error);
                                                Swal.fire('Error', 'Terjadi kesalahan saat menolak pengajuan.', 'error');
                                            });
                                        }
                                    });
                                });
                            });

                            // Event listener untuk tombol tandai sudah dibaca
                            const markReadButtons = Swal.getHtmlContainer().querySelectorAll('.mark-read-button');
                            markReadButtons.forEach(button => {
                                button.addEventListener('click', function() {
                                    const requestId = this.dataset.id;
                                    const requestType = this.dataset.type;
                                    
                                    fetch('<?php echo BASE_URL; ?>api/mark_notification_read.php', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json',
                                        },
                                        body: JSON.stringify({ id: requestId, type: requestType })
                                    })
                                    .then(response => response.json())
                                    .then(data => {
                                        if (data.success) {
                                            // Hapus card notifikasi dari tampilan
                                            this.closest('.notification-card').remove();
                                            
                                            // Tampilkan pesan sukses
                                            Swal.fire({
                                                icon: 'success',
                                                title: 'Berhasil',
                                                text: data.message,
                                                timer: 1500,
                                                showConfirmButton: false
                                            }).then(() => {
                                                // Refresh halaman setelah notifikasi berhasil
                                                window.location.reload();
                                            });
                                        } else {
                                            Swal.fire({
                                                icon: 'error',
                                                title: 'Gagal',
                                                text: data.message
                                            });
                                        }
                                    })
                                    .catch(error => {
                                        console.error('Error:', error);
                                        Swal.fire({
                                            icon: 'error',
                                            title: 'Error',
                                            text: 'Terjadi kesalahan saat memproses permintaan'
                                        });
                                    });
                                });
                            });
                        }
                    });
                } else {
                    Swal.fire({
                        title: 'Tidak Ada Notifikasi',
                        text: 'Saat ini tidak ada notifikasi persetujuan maupun pengajuan pending untuk Anda.',
                        icon: 'info',
                        showCancelButton: true,
                        confirmButtonText: 'Logout',
                        cancelButtonText: 'Tutup',
                        reverseButtons: true
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = '<?php echo BASE_URL; ?>logout.php';
                        }
                    });
                }
            });
        }
    });
</script>

<!-- Custom CSS untuk notifikasi (bisa ditambahkan ke mobile-app.css atau file terpisah) -->
<style>
    .notification-button {
        position: absolute;
        top: 20px;
        right: 20px;
        font-size: 24px;
        color: #6a11cb; /* Warna ikon bel */
        text-decoration: none;
        transition: transform 0.2s;
    }

    .notification-button:hover {
        transform: scale(1.1);
        color: #5a0eab; /* Warna sedikit lebih gelap saat hover */
    }

    /* Tambahkan style untuk indikator pending jika ada */
    .notification-button.pending-approver-button {
        color: #ffc107; /* Warna kuning untuk notifikasi pending */
    }

    .notification-button.pending-approver-button:hover {
        color: #e0a800; /* Warna kuning lebih gelap saat hover */
    }

    .notification-button .notification-count {
        position: absolute;
        top: -5px;
        right: -5px;
        font-size: 12px;
        padding: 3px 6px;
        border-radius: 50%;
        background-color: #dc3545; /* Warna merah untuk badge */
        color: white;
        border: 1px solid white; /* Border putih agar lebih terlihat */
    }

    /* .notification-button .notification-count.pending-count {
        background-color: #ffc107; /* Warna kuning untuk badge pending */
        color: #343a40; /* Warna teks gelap untuk kontras */
        border: 1px solid white;
    } */

    .notification-popup {
        width: 90% !important;
        max-width: 500px;
    }

    .notification-card {
        border: 1px solid #e0e0e0;
        border-left: 5px solid #6a11cb; /* Garis warna di kiri */
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .notification-card .card-title {
        font-size: 1.1em;
        font-weight: 600;
        color: #333;
    }

    .notification-card .card-text {
        font-size: 0.9em;
        color: #555;
    }

    .swal2-confirm-button, .swal2-cancel-button {
        font-size: 0.9em;
        padding: 8px 15px;
    }
</style>

<!-- Tambahkan di bagian head atau sebelum closing body -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.css"/>
<script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.umd.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inisialisasi Fancybox
        Fancybox.bind("[data-fancybox]", {
            // Custom options
            Image: {
                zoom: true,
                wheel: "slide",
                click: "zoom",
                dblclick: "zoom",
                fit: "contain",
                transition: "fade"
            },
            Toolbar: {
                display: [
                    { id: "prev", position: "center" },
                    { id: "counter", position: "center" },
                    { id: "next", position: "center" },
                    "zoom",
                    "slideshow",
                    "fullscreen",
                    "download",
                    "close",
                ],
            },
            Thumbs: {
                autoStart: true,
                axis: "x"
            },
            // Tambahkan opsi untuk menutup notifikasi saat membuka Fancybox
            on: {
                "reveal": (fancybox) => {
                    // Tutup SweetAlert2 jika terbuka
                    if (Swal.isVisible()) {
                        Swal.close();
                    }
                },
                "done": (fancybox, slide) => {
                    // Pastikan gambar dimuat dengan benar
                    const img = slide.el.querySelector('img');
                    if (img) {
                        img.onerror = function() {
                            console.error('Error loading image:', img.src);
                            fancybox.close();
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'Gagal memuat gambar. Silakan coba lagi.'
                            });
                        };
                    }
                }
            }
        });
    });
</script>

<style>
    /* Perbaikan style untuk Fancybox */
    .fancybox__container {
        --fancybox-bg: rgba(0, 0, 0, 0.95);
        z-index: 99999 !important;
    }

    .fancybox__backdrop {
        z-index: 99998 !important;
    }

    .fancybox__content {
        z-index: 99999 !important;
        background: transparent;
    }

    .fancybox__image {
        max-width: 100%;
        max-height: 90vh;
        object-fit: contain;
    }

    .fancybox__toolbar {
        --fancybox-accent-color: #4e73df;
        z-index: 100000 !important;
    }

    .fancybox__button {
        color: #fff;
    }

    .fancybox__button:hover {
        color: #4e73df;
    }

    .fancybox__caption {
        color: #fff;
        font-size: 14px;
        padding: 12px;
        text-align: center;
        z-index: 100000 !important;
    }

    .fancybox__nav button {
        color: #fff;
    }

    .fancybox__nav button:hover {
        color: #4e73df;
    }

    .fancybox__thumb {
        border-radius: 4px;
        opacity: 0.5;
        transition: opacity 0.2s;
    }

    .fancybox__thumb:hover,
    .fancybox__thumb.is-selected {
        opacity: 1;
    }
</style>

<!-- Tambahkan script untuk toggle info section -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const toggleButton = document.getElementById('toggleInfoButton');
        const infoContent = document.getElementById('infoContent');
        const toggleIcon = document.getElementById('toggleInfoIcon');
        const toggleText = document.getElementById('infoToggleText');

        // Tambahkan CSS untuk class show jika belum ada
        if (!document.querySelector('style[data-toggle-info]')) {
            const style = document.createElement('style');
            style.setAttribute('data-toggle-info', 'true');
            style.textContent = `
                .info-content {
                    transition: all 0.3s ease;
                    overflow: hidden;
                }
                .info-content.hidden {
                    max-height: 0 !important;
                    opacity: 0;
                    margin: 0;
                    padding: 0;
                }
                .toggle-arrow-button.active i {
                    transform: rotate(180deg) !important;
                }
            `;
            document.head.appendChild(style);
        }

        if (toggleButton && infoContent && toggleIcon && toggleText) {
            // Event listener untuk tombol toggle
            toggleButton.addEventListener('click', function() {
                const isContentVisible = !infoContent.classList.contains('hidden');

                if (isContentVisible) {
                    // Sembunyikan konten
                    infoContent.classList.add('hidden');
                    toggleIcon.classList.remove('fa-chevron-up');
                    toggleIcon.classList.add('fa-chevron-down');
                    toggleButton.classList.remove('active');
                    toggleText.textContent = 'Tampilkan Info';
                } else {
                    // Tampilkan konten
                    infoContent.classList.remove('hidden');
                    toggleIcon.classList.remove('fa-chevron-down');
                    toggleIcon.classList.add('fa-chevron-up');
                    toggleButton.classList.add('active');
                    toggleText.textContent = 'Tutup Info';
                }
            });

            // Event listener untuk teks toggle
            toggleText.addEventListener('click', function() {
                toggleButton.click();
            });
        }
    });
</script>

<style>
    /* Style untuk info section */
    .info-section {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        margin-bottom: 20px;
        overflow: hidden;
    }

    .info-title {
        padding: 15px 20px;
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
        font-weight: 600;
        color: #4e73df;
    }

    .info-content {
        padding: 20px;
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .info-content.hidden {
        max-height: 0 !important;
        opacity: 0;
        margin: 0;
        padding: 0;
    }

    .toggle-arrow-button {
        background: none;
        border: none;
        color: #4e73df;
        font-size: 14px;
        cursor: pointer;
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        background-color: rgba(78, 115, 223, 0.1);
        padding: 0;
    }

    .toggle-arrow-button:hover {
        background-color: rgba(78, 115, 223, 0.2);
    }

    .toggle-arrow-button i {
        transition: transform 0.3s ease;
    }

    .toggle-arrow-button.active i {
        transform: rotate(180deg);
    }

    .info-content ol {
        margin: 0;
        padding-left: 20px;
    }

    .info-content li {
        margin-bottom: 8px;
        color: #5a5c69;
        font-size: 14px;
    }

    .info-content li:last-child {
        margin-bottom: 0;
    }
</style>

<style>
.pengumuman-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.pengumuman-item {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pengumuman-image-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f8f9fc;
    padding: 10px;
}

.pengumuman-image {
    width: 100%;
    max-height: 300px;
    object-fit: contain;
    border-radius: 4px;
    transition: transform 0.2s;
}

.pengumuman-image:hover {
    transform: scale(1.02);
}

.pengumuman-info {
    padding: 15px;
    text-align: center;
}

.pengumuman-title {
    color: #4e73df;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 1.1em;
}

.pengumuman-date {
    color: #6c757d;
    font-size: 0.9em;
    margin: 0;
}

.pengumuman-date i {
    margin-right: 4px;
}
</style>

<!-- Tambahkan CSS dan JS Swiper -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inisialisasi Swiper
    const swiper = new Swiper('.pengumuman-swiper', {
        slidesPerView: 1,
        spaceBetween: 20,
        loop: true,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
    });
});
</script>

<style>
/* ... existing styles ... */

/* Style untuk Swiper */
.pengumuman-swiper {
    width: 100%;
    padding: 20px 0;
}

.swiper-slide {
    height: auto;
}

.pengumuman-item {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.pengumuman-image-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f8f9fc;
    padding: 10px;
    flex: 1;
}

.pengumuman-image {
    width: 100%;
    height: 300px;
    object-fit: contain;
    border-radius: 4px;
}

.pengumuman-info {
    padding: 15px;
    text-align: center;
}

.pengumuman-title {
    color: #4e73df;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 1.1em;
}

.pengumuman-date {
    color: #6c757d;
    font-size: 0.9em;
    margin: 0;
}

.pengumuman-date i {
    margin-right: 4px;
}

/* Style untuk navigasi Swiper */
.swiper-button-next,
.swiper-button-prev {
    color: #ffffff;
    background: #4e73df;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 18px;
    font-weight: bold;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
    background: #2e59d9;
    color: #ffffff;
}

.swiper-pagination-bullet {
    background: #4e73df;
}

.swiper-pagination-bullet-active {
    background: #2e59d9;
}

/* Penyesuaian posisi tombol navigasi */
.swiper-button-prev {
    left: 10px;
}

.swiper-button-next {
    right: 10px;
}

/* Penyesuaian posisi pagination */
.swiper-pagination {
    bottom: 5px;
}
</style>

</div>
</body>
</html>

<script>
    // Fungsi untuk menunggu Chart.js dimuat
    function waitForChartJs(callback) {
        if (typeof Chart !== 'undefined') {
            callback();
        } else {
            setTimeout(function() {
                waitForChartJs(callback);
            }, 100);
        }
    }

    // Data dari PHP untuk grafik
    const chartData = {
        tepatWaktu: <?php echo $total_presensi_bulan_ini - $total_terlambat_bulan_ini; ?>,
        terlambat: <?php echo $total_terlambat_bulan_ini; ?>,
        tidakAbsenPulang: <?php 
            $query = "SELECT COUNT(*) as total FROM presensi 
                     WHERE user_id = '$user_id' 
                     AND (jam_pulang IS NULL OR jam_pulang = '00:00:00')
                     AND MONTH(tanggal) = '$bulan' 
                     AND YEAR(tanggal) = '$tahun'";
            $result = mysqli_query($conn, $query);
            $row = mysqli_fetch_assoc($result);
            echo $row['total'] ?? 0;
        ?>,
        tidakAbsen: <?php echo $tidak_absen_efektif; ?>
    };

    // Debug: Tampilkan data di console
    console.log('Data Grafik:', chartData);

    // Tunggu Chart.js dimuat sebelum membuat grafik
    waitForChartJs(function() {
        document.addEventListener('DOMContentLoaded', function() {
            try {
                const canvas = document.getElementById('absensiChart');
                if (!canvas) {
                    console.error('Canvas element tidak ditemukan!');
                    return;
                }

                const ctx = canvas.getContext('2d');
                if (!ctx) {
                    console.error('Tidak dapat mendapatkan context dari canvas!');
                    return;
                }

                console.log('Membuat grafik dengan data:', chartData);

                const absensiChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['Tepat Waktu', 'Terlambat', 'Tidak Absen Pulang', 'Tidak Absen/Alpa'],
                        datasets: [{
                            label: 'Jumlah Absensi',
                            data: [chartData.tepatWaktu, chartData.terlambat, chartData.tidakAbsenPulang, chartData.tidakAbsen],
                            backgroundColor: [
                                'rgba(75, 192, 192, 0.6)', // Tepat Waktu (Hijau)
                                'rgba(255, 99, 132, 0.6)', // Terlambat (Merah)
                                'rgba(255, 205, 86, 0.6)', // Tidak Absen Pulang (Kuning)
                                'rgba(153, 102, 255, 0.6)' // Tidak Absen/Alpa (Ungu)
                            ],
                            borderColor: [
                                'rgba(75, 192, 192, 1)',
                                'rgba(255, 99, 132, 1)',
                                'rgba(255, 205, 86, 1)',
                                'rgba(153, 102, 255, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        if (Number.isInteger(value)) {
                                            return value;
                                        }
                                    }
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false // Sembunyikan legenda karena label sudah jelas
                            },
                            title: {
                                display: true,
                                text: 'Statistik Absensi Karyawan Bulan Ini'
                            }
                        }
                    }
                });

                console.log('Grafik berhasil dibuat!');
            } catch (error) {
                console.error('Error saat membuat grafik:', error);
                alert('Terjadi kesalahan saat membuat grafik. Silakan refresh halaman.');
            }
        });
    });
</script>
