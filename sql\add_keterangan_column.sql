-- <PERSON>bah kolom keterangan ke tabel users jika belum ada
-- Kolom ini digunakan untuk mengidentifikasi karyawan koperasi

-- Cek apakah kolom keterangan sudah ada
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'users'
    AND COLUMN_NAME = 'keterangan'
);

-- Tambahkan kolom keterangan jika belum ada
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE users ADD COLUMN keterangan TEXT DEFAULT NULL AFTER jabatan',
    'SELECT "Kolom keterangan sudah ada" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
