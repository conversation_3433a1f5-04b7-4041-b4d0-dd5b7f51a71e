<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// <PERSON>k apakah request adalah POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Ambil data dari request
    $type = $_POST['type'] ?? '';
    $request_id = $_POST['request_id'] ?? '';
    
    // Validasi input
    if (empty($type) || empty($request_id)) {
        echo json_encode(['success' => false, 'message' => 'Data tidak lengkap']);
        exit;
    }
    
    // Tandai notifikasi sebagai telah dibaca
    if (markNotificationAsRead($type, $request_id)) {
        echo json_encode(['success' => true, 'message' => 'Notifikasi ditandai sebagai telah dibaca']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Gagal menandai notifikasi']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Method tidak diizinkan']);
} 