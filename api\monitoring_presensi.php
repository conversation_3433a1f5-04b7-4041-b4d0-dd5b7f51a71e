<?php
require_once '../config/database.php';
require_once '../config/config.php';

// Tambahkan header CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

function validateApiKey($api_key) {
    return $api_key === 'absensiku_api_key_2023';
}

// Fungsi untuk menyimpan gambar base64
function saveBase64Image($base64, $prefix = 'foto') {
    if (!$base64) return '';
    $foto_data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64));
    $foto_name = $prefix . '_' . time() . '_' . rand(1000,9999) . '.jpg';
    $upload_dir = UPLOAD_PATH;
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
    $foto_path = $upload_dir . $foto_name;
    file_put_contents($foto_path, $foto_data);
    return $foto_name;
}

// Fungsi untuk menentukan status presensi
function tentukanStatus($tanggal, $jam_masuk) {
    if (empty($jam_masuk)) return '-';
    $hari = date('N', strtotime($tanggal)); // 1=Senin, 5=Jumat
    $jamMasuk = strtotime($jam_masuk);

    if ($hari >= 1 && $hari <= 4) {
        $batas = strtotime('07:15:00');
    } elseif ($hari == 5) {
        $batas = strtotime('06:30:00');
    } else {
        return '-'; // Sabtu & Minggu
    }

    return ($jamMasuk > $batas) ? 'Terlambat' : 'Tepat Waktu';
}

$method = $_SERVER['REQUEST_METHOD'];
$input = file_get_contents('php://input');
$data = json_decode($input, true);
if (!$data) $data = $_POST;

// Untuk GET, ambil api_key dari $_GET jika tidak ada di body
if ($method === 'GET') {
    if (!isset($data['api_key']) && isset($_GET['api_key'])) {
        $data['api_key'] = $_GET['api_key'];
    }
}

if (!isset($data['api_key']) || !validateApiKey($data['api_key'])) {
    echo json_encode(['status' => 'error', 'message' => 'API key tidak valid']);
    exit;
}

// ======================
// GET: List/filter presensi
// ======================
if ($method === 'GET') {
    $where = [];
    if (isset($_GET['id'])) $where[] = "id = '" . mysqli_real_escape_string($conn, $_GET['id']) . "'";
    if (isset($_GET['user_id'])) $where[] = "user_id = '" . mysqli_real_escape_string($conn, $_GET['user_id']) . "'";
    if (isset($_GET['tanggal'])) $where[] = "tanggal = '" . mysqli_real_escape_string($conn, $_GET['tanggal']) . "'";

    $sql = "SELECT id, user_id, tanggal, jam_masuk, foto_masuk, lokasi_masuk, jam_pulang, foto_pulang, lokasi_pulang, status, keterangan, created_at, updated_at 
            FROM presensi";
    if ($where) $sql .= " WHERE " . implode(' AND ', $where);
    $sql .= " ORDER BY tanggal DESC, jam_masuk ASC";

    $result = mysqli_query($conn, $sql);
    $rows = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            // Jika status kosong di DB, hitung ulang
            if (empty($row['status'])) {
                $row['status'] = tentukanStatus($row['tanggal'], $row['jam_masuk']);
            }
            $rows[] = $row;
        }
    }
    echo json_encode(['status' => 'success', 'data' => $rows]);
    exit;
}

// ======================
// POST: Tambah presensi
// ======================
if ($method === 'POST') {
    $user_id = mysqli_real_escape_string($conn, $data['user_id'] ?? '');
    $tanggal = mysqli_real_escape_string($conn, $data['tanggal'] ?? '');
    $jam_masuk = mysqli_real_escape_string($conn, $data['jam_masuk'] ?? '');
    $lokasi_masuk = mysqli_real_escape_string($conn, $data['lokasi_masuk'] ?? '');
    $jam_pulang = mysqli_real_escape_string($conn, $data['jam_pulang'] ?? '');
    $lokasi_pulang = mysqli_real_escape_string($conn, $data['lokasi_pulang'] ?? '');
    $keterangan = mysqli_real_escape_string($conn, $data['keterangan'] ?? '');
    $created_at = date('Y-m-d H:i:s');
    $updated_at = $created_at;

    // Hitung status otomatis
    $status = tentukanStatus($tanggal, $jam_masuk);

    // Handle foto masuk base64
    $foto_masuk = '';
    if (!empty($data['foto_masuk_base64'])) {
        $foto_masuk = saveBase64Image($data['foto_masuk_base64'], 'foto_masuk');
    } else {
        $foto_masuk = mysqli_real_escape_string($conn, $data['foto_masuk'] ?? '');
    }
    // Handle foto pulang base64
    $foto_pulang = '';
    if (!empty($data['foto_pulang_base64'])) {
        $foto_pulang = saveBase64Image($data['foto_pulang_base64'], 'foto_pulang');
    } else {
        $foto_pulang = mysqli_real_escape_string($conn, $data['foto_pulang'] ?? '');
    }

    $sql = "INSERT INTO presensi (user_id, tanggal, jam_masuk, foto_masuk, lokasi_masuk, jam_pulang, foto_pulang, lokasi_pulang, status, keterangan, created_at, updated_at) 
            VALUES ('$user_id', '$tanggal', '$jam_masuk', '$foto_masuk', '$lokasi_masuk', " . ($jam_pulang ? "'$jam_pulang'" : "NULL") . ", '$foto_pulang', '$lokasi_pulang', '$status', '$keterangan', '$created_at', '$updated_at')";

    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Data presensi berhasil ditambahkan']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal menambah data presensi: ' . mysqli_error($conn)]);
    }
    exit;
}

// ======================
// PUT: Edit presensi
// ======================
if ($method === 'PUT') {
    parse_str(file_get_contents('php://input'), $put_vars);
    $id = mysqli_real_escape_string($conn, $data['id'] ?? $put_vars['id'] ?? '');
    if (!$id) {
        echo json_encode(['status' => 'error', 'message' => 'ID presensi wajib diisi']);
        exit;
    }
    $fields = [];
    foreach (['user_id', 'tanggal', 'jam_masuk', 'lokasi_masuk', 'jam_pulang', 'lokasi_pulang', 'status', 'keterangan'] as $col) {
        $val = $data[$col] ?? $put_vars[$col] ?? null;
        if (!is_null($val)) {
            $fields[] = "$col = '" . mysqli_real_escape_string($conn, $val) . "'";
        }
    }
    // Handle foto masuk base64
    if (!empty($data['foto_masuk_base64'] ?? $put_vars['foto_masuk_base64'] ?? '')) {
        $foto_masuk = saveBase64Image($data['foto_masuk_base64'] ?? $put_vars['foto_masuk_base64'], 'foto_masuk');
        $fields[] = "foto_masuk = '$foto_masuk'";
    } elseif (!empty($data['foto_masuk'] ?? $put_vars['foto_masuk'] ?? '')) {
        $fields[] = "foto_masuk = '" . mysqli_real_escape_string($conn, $data['foto_masuk'] ?? $put_vars['foto_masuk']) . "'";
    }
    // Handle foto pulang base64
    if (!empty($data['foto_pulang_base64'] ?? $put_vars['foto_pulang_base64'] ?? '')) {
        $foto_pulang = saveBase64Image($data['foto_pulang_base64'] ?? $put_vars['foto_pulang_base64'], 'foto_pulang');
        $fields[] = "foto_pulang = '$foto_pulang'";
    } elseif (!empty($data['foto_pulang'] ?? $put_vars['foto_pulang'] ?? '')) {
        $fields[] = "foto_pulang = '" . mysqli_real_escape_string($conn, $data['foto_pulang'] ?? $put_vars['foto_pulang']) . "'";
    }
    $fields[] = "updated_at = '" . date('Y-m-d H:i:s') . "'";

    $sql = "UPDATE presensi SET " . implode(', ', $fields) . " WHERE id = '$id'";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Data presensi berhasil diupdate']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal update data presensi: ' . mysqli_error($conn)]);
    }
    exit;
}

// ======================
// DELETE: Hapus presensi
// ======================
if ($method === 'DELETE') {
    parse_str(file_get_contents('php://input'), $del_vars);
    $id = mysqli_real_escape_string($conn, $data['id'] ?? $del_vars['id'] ?? '');
    if (!$id) {
        echo json_encode(['status' => 'error', 'message' => 'ID presensi wajib diisi']);
        exit;
    }
    $sql = "DELETE FROM presensi WHERE id = '$id'";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Data presensi berhasil dihapus']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal menghapus data presensi: ' . mysqli_error($conn)]);
    }
    exit;
}

echo json_encode(['status' => 'error', 'message' => 'Metode tidak didukung']);
