<?php
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

checkAccess('admin');

$nama_karyawan = isset($_GET['nama_karyawan']) ? clean($_GET['nama_karyawan']) : '';
$periode = isset($_GET['periode']) ? clean($_GET['periode']) : '';

if (empty($nama_karyawan) || empty($periode)) {
    $_SESSION['error'] = 'Nama karyawan dan periode harus dipilih untuk export Excel!';
    header('Location: laporan_harian.php');
    exit;
}

// Set header untuk download file Excel
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="Laporan_Kinerja_Harian_' . str_replace(' ', '_', $nama_karyawan) . '_' . $periode . '.xlsx"');
header('Cache-Control: max-age=0');

// Query untuk mengambil data berdasarkan filter
$query = "SELECT * FROM laporan_harian 
          WHERE nama_karyawan = '" . mysqli_real_escape_string($conn, $nama_karyawan) . "' 
          AND DATE_FORMAT(tanggal, '%Y-%m') = '" . mysqli_real_escape_string($conn, $periode) . "'
          ORDER BY tanggal ASC";
$result = mysqli_query($conn, $query);

// Buat file Excel sederhana dengan format XML
echo '<?xml version="1.0" encoding="UTF-8"?>';
?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 
<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Title>Laporan Kinerja Harian - <?php echo htmlspecialchars($nama_karyawan); ?></Title>
  <Author>Sistem Absensi</Author>
  <Created><?php echo date('Y-m-d\TH:i:s\Z'); ?></Created>
</DocumentProperties>

<Styles>
  <Style ss:ID="Header">
    <Font ss:Bold="1" ss:Size="14" ss:Color="#FFFFFF"/>
    <Interior ss:Color="#4CAF50" ss:Pattern="Solid"/>
    <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
    <Borders>
      <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
      <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
      <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
      <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
    </Borders>
  </Style>
  
  <Style ss:ID="Info">
    <Font ss:Bold="1" ss:Size="12"/>
    <Interior ss:Color="#E8F5E8" ss:Pattern="Solid"/>
    <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
    <Borders>
      <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
      <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
      <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
      <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
    </Borders>
  </Style>
  
  <Style ss:ID="ColumnHeader">
    <Font ss:Bold="1" ss:Size="11" ss:Color="#FFFFFF"/>
    <Interior ss:Color="#2E7D32" ss:Pattern="Solid"/>
    <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
    <Borders>
      <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
      <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
      <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
      <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
    </Borders>
  </Style>
  
  <Style ss:ID="Data">
    <Alignment ss:Vertical="Top" ss:WrapText="1"/>
    <Borders>
      <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
      <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
      <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
      <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
    </Borders>
  </Style>
  
  <Style ss:ID="DataCenter">
    <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
    <Borders>
      <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
      <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
      <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
      <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
    </Borders>
  </Style>
</Styles>

<Worksheet ss:Name="Laporan Kinerja Harian">
  <Table>
    <Column ss:Width="40"/>
    <Column ss:Width="80"/>
    <Column ss:Width="80"/>
    <Column ss:Width="250"/>
    <Column ss:Width="120"/>
    <Column ss:Width="120"/>
    
    <!-- Header -->
    <Row ss:Height="30">
      <Cell ss:MergeAcross="5" ss:StyleID="Header">
        <Data ss:Type="String">LAPORAN KINERJA HARIAN</Data>
      </Cell>
    </Row>
    
    <!-- Info Karyawan -->
    <Row ss:Height="25">
      <Cell ss:MergeAcross="5" ss:StyleID="Info">
        <Data ss:Type="String">Nama Karyawan: <?php echo htmlspecialchars($nama_karyawan); ?></Data>
      </Cell>
    </Row>
    
    <!-- Info Periode -->
    <Row ss:Height="25">
      <Cell ss:MergeAcross="5" ss:StyleID="Info">
        <Data ss:Type="String">Periode: <?php echo date('F Y', strtotime($periode . '-01')); ?></Data>
      </Cell>
    </Row>
    
    <!-- Info Tanggal Export -->
    <Row ss:Height="25">
      <Cell ss:MergeAcross="5" ss:StyleID="Info">
        <Data ss:Type="String">Tanggal Export: <?php echo date('d F Y, H:i:s'); ?></Data>
      </Cell>
    </Row>
    
    <!-- Column Headers -->
    <Row ss:Height="25">
      <Cell ss:StyleID="ColumnHeader"><Data ss:Type="String">No</Data></Cell>
      <Cell ss:StyleID="ColumnHeader"><Data ss:Type="String">Tanggal</Data></Cell>
      <Cell ss:StyleID="ColumnHeader"><Data ss:Type="String">Periode</Data></Cell>
      <Cell ss:StyleID="ColumnHeader"><Data ss:Type="String">Keterangan</Data></Cell>
      <Cell ss:StyleID="ColumnHeader"><Data ss:Type="String">Foto</Data></Cell>
      <Cell ss:StyleID="ColumnHeader"><Data ss:Type="String">Waktu Input</Data></Cell>
    </Row>
    
    <?php
    $no = 1;
    if (mysqli_num_rows($result) > 0) {
        while ($row = mysqli_fetch_assoc($result)) {
            echo '<Row ss:Height="60">';
            echo '<Cell ss:StyleID="DataCenter"><Data ss:Type="Number">' . $no++ . '</Data></Cell>';
            echo '<Cell ss:StyleID="DataCenter"><Data ss:Type="String">' . date('d/m/Y', strtotime($row['tanggal'])) . '</Data></Cell>';
            echo '<Cell ss:StyleID="DataCenter"><Data ss:Type="String">' . htmlspecialchars($row['periode']) . '</Data></Cell>';
            echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($row['keterangan']) . '</Data></Cell>';

            // Kolom foto - untuk Excel XML, kita berikan informasi file dengan status
            echo '<Cell ss:StyleID="DataCenter">';
            if ($row['foto']) {
                $status = file_exists('../uploads/' . $row['foto']) ? '✓' : '✗';
                echo '<Data ss:Type="String">' . $status . ' ' . htmlspecialchars($row['foto']) . '</Data>';
                echo '<Comment>';
                echo '<ss:Data xmlns="http://www.w3.org/TR/REC-html40">';
                echo '<Font>File foto: ' . htmlspecialchars($row['foto']) . '</Font>';
                if (file_exists('../uploads/' . $row['foto'])) {
                    echo '<Font>Status: File tersedia di server</Font>';
                    echo '<Font>Path: uploads/' . htmlspecialchars($row['foto']) . '</Font>';
                } else {
                    echo '<Font>Status: File tidak ditemukan di server</Font>';
                    echo '<Font>Kemungkinan file telah dihapus atau dipindah</Font>';
                }
                echo '</ss:Data>';
                echo '</Comment>';
            } else {
                echo '<Data ss:Type="String">Tidak Ada Foto</Data>';
            }
            echo '</Cell>';

            echo '<Cell ss:StyleID="DataCenter"><Data ss:Type="String">' . date('d/m/Y H:i', strtotime($row['created_at'])) . '</Data></Cell>';
            echo '</Row>';
        }
    } else {
        echo '<Row ss:Height="50">';
        echo '<Cell ss:MergeAcross="5" ss:StyleID="DataCenter">';
        echo '<Data ss:Type="String">Tidak ada data laporan untuk karyawan ' . htmlspecialchars($nama_karyawan) . ' pada periode ' . date('F Y', strtotime($periode . '-01')) . '</Data>';
        echo '</Cell>';
        echo '</Row>';
    }
    ?>
    
    <!-- Footer -->
    <Row ss:Height="30">
      <Cell ss:MergeAcross="5" ss:StyleID="Info">
        <Data ss:Type="String">Laporan digenerate otomatis pada <?php echo date('d F Y, H:i:s'); ?></Data>
      </Cell>
    </Row>
    
  </Table>
</Worksheet>
</Workbook>
