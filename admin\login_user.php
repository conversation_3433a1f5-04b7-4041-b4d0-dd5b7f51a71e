<?php
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

checkAccess('admin');

// Handle hapus data
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    $id = intval($_POST['id']);
    if (mysqli_query($conn, "DELETE FROM status_user WHERE id = $id")) {
        $_SESSION['success'] = "Data berhasil dihapus.";
    } else {
        $_SESSION['error'] = "Gagal menghapus data: " . mysqli_error($conn);
    }
    header("Location: login_user.php");
    exit;
}

// Handle AJAX request untuk toggle status
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && in_array($_POST['action'], ['toggle_login', 'toggle_akun'])) {
    $id = intval($_POST['id']);
    $response = ['success' => false];

    if ($_POST['action'] === 'toggle_login') {
        $query = "SELECT status_login FROM status_user WHERE id = $id";
        $result = mysqli_query($conn, $query);
        if ($row = mysqli_fetch_assoc($result)) {
            $new_status = ($row['status_login'] === 'login') ? 'tidak' : 'login';
            mysqli_query($conn, "UPDATE status_user SET status_login = '$new_status' WHERE id = $id");
            $response = ['success' => true, 'new_status' => $new_status];
        }
    }

    if ($_POST['action'] === 'toggle_akun') {
        $query = "SELECT status_akun FROM status_user WHERE id = $id";
        $result = mysqli_query($conn, $query);
        if ($row = mysqli_fetch_assoc($result)) {
            $new_status = ($row['status_akun'] === 'aktif') ? 'tidak aktif' : 'aktif';
            mysqli_query($conn, "UPDATE status_user SET status_akun = '$new_status' WHERE id = $id");
            $response = ['success' => true, 'new_status' => $new_status];
        }
    }

    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Ambil data
$query = "SELECT * FROM status_user ORDER BY id DESC";
$result = mysqli_query($conn, $query);

// Header HTML
include_once '../includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Status User</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item active">Status User</li>
    </ol>

    <div id="alert-area"></div>

    <?php if (!empty($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-users me-1"></i>
            Daftar Status User
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="statusUserTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>No.</th>
                            <th>NIK</th>
                            <th>Status Login</th>
                            <th>Status Akun</th>
                            <th>Device ID</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $no = 1;
                        while ($row = mysqli_fetch_assoc($result)) {
                            echo '<tr>
                                <td>' . $no++ . '</td>
                                <td>' . htmlspecialchars($row['nik']) . '</td>
                                <td>
                                    <button class="btn btn-sm toggle-login ' . ($row['status_login'] == 'login' ? 'btn-success' : 'btn-secondary') . '" data-id="' . $row['id'] . '">
                                        ' . $row['status_login'] . '
                                    </button>
                                </td>
                                <td>
                                    <button class="btn btn-sm toggle-akun ' . ($row['status_akun'] == 'aktif' ? 'btn-success' : 'btn-secondary') . '" data-id="' . $row['id'] . '">
                                        ' . $row['status_akun'] . '
                                    </button>
                                </td>
                                <td>' . htmlspecialchars($row['device_id']) . '</td>
                                <td>
                                    <form method="post" class="d-inline">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="id" value="' . $row['id'] . '">
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm(\'Yakin ingin menghapus data ini?\')">
                                            <i class="fas fa-trash"></i> Hapus
                                        </button>
                                    </form>
                                </td>
                            </tr>';
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>

<script>
    $(document).ready(function() {
        $('#statusUserTable').DataTable({ responsive: true });

        // Fungsi Toggle Login
        $('.toggle-login').click(function() {
            const button = $(this);
            const id = button.data('id');

            $.post('', { action: 'toggle_login', id: id }, function(response) {
                if (response.success) {
                    button.text(response.new_status);
                    button.toggleClass('btn-success btn-secondary');
                    showAlert('Status login berhasil diubah.', 'success');
                }
            }, 'json');
        });

        // Fungsi Toggle Akun
        $('.toggle-akun').click(function() {
            const button = $(this);
            const id = button.data('id');

            $.post('', { action: 'toggle_akun', id: id }, function(response) {
                if (response.success) {
                    button.text(response.new_status);
                    button.toggleClass('btn-success btn-secondary');
                    showAlert('Status akun berhasil diubah.', 'success');
                }
            }, 'json');
        });

        // Tampilkan alert
        function showAlert(message, type) {
            $('#alert-area').html(`
                <div class="alert alert-${type} alert-dismissible fade show mt-2" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `);
        }
    });
</script>
