<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Cek login
if (!isLoggedIn()) {
    header("Location: login.php");
    exit();
}

// Cek role admin
if (!isAdmin()) {
    header("Location: index.php");
    exit();
}

// Set default bulan dan tahun ke bulan sebelumnya jika belum diset
if (!isset($_GET['bulan']) || !isset($_GET['tahun'])) {
    $bulan = date('m', strtotime('-1 month'));
    $tahun = date('Y', strtotime('-1 month'));
} else {
    $bulan = $_GET['bulan'];
    $tahun = $_GET['tahun'];
}

// Ambil pengaturan denda (dipindahkan ke sini agar selalu tersedia dan diberi nilai default)
$sql_denda = "SELECT * FROM denda ORDER BY id DESC LIMIT 1";
$result_denda = $conn->query($sql_denda);
$denda = $result_denda->fetch_assoc();

// Handle jika denda tidak ditemukan (berikan nilai default untuk menghindari undefined variable/index)
if (!$denda) {
    $denda = [
        'denda_masuk' => 0,
        'denda_pulang' => 0,
        'denda_tidak_absen' => 0,
        'denda_tidak_absen_pulang' => 0
    ];
}

// Hitung total hari kerja
$total_hari = cal_days_in_month(CAL_GREGORIAN, $bulan, $tahun);
$total_hari_libur = 0;

// Hitung hari Minggu
$first_day = mktime(0, 0, 0, $bulan, 1, $tahun);
$last_day = mktime(0, 0, 0, $bulan + 1, 0, $tahun);
for ($i = $first_day; $i <= $last_day; $i += 86400) {
    if (date('N', $i) == 7) { // 7 adalah hari Minggu
        $total_hari_libur++;
    }
}

// Hitung hari libur nasional
$sql_libur = "SELECT COUNT(*) as total FROM hari_libur 
              WHERE MONTH(tanggal) = ? AND YEAR(tanggal) = ?";
$stmt_libur = $conn->prepare($sql_libur);
$stmt_libur->bind_param("ss", $bulan, $tahun);
$stmt_libur->execute();
$result_libur = $stmt_libur->get_result();
$row_libur = $result_libur->fetch_assoc();
$total_hari_libur += $row_libur['total'];

// Hitung hari kerja berdasarkan konfigurasi hari kerja
$sql_hari_kerja = "SELECT COUNT(*) as total FROM hari_kerja WHERE status = 1";
$result_hari_kerja = mysqli_query($conn, $sql_hari_kerja);
$row_hari_kerja = mysqli_fetch_assoc($result_hari_kerja);
$total_hari_kerja = $row_hari_kerja['total'];

// Hitung total hari kerja dalam bulan
$total_hari_kerja_bulan = 0;
for ($i = $first_day; $i <= $last_day; $i += 86400) {
    $hari = date('N', $i); // 1 (Senin) sampai 7 (Minggu)
    $sql_check = "SELECT status FROM hari_kerja WHERE hari = CASE 
        WHEN $hari = 1 THEN 'Senin'
        WHEN $hari = 2 THEN 'Selasa'
        WHEN $hari = 3 THEN 'Rabu'
        WHEN $hari = 4 THEN 'Kamis'
        WHEN $hari = 5 THEN 'Jumat'
        WHEN $hari = 6 THEN 'Sabtu'
        WHEN $hari = 7 THEN 'Minggu'
    END";
    $result_check = mysqli_query($conn, $sql_check);
    $row_check = mysqli_fetch_assoc($result_check);
    if ($row_check && $row_check['status'] == 1) {
        $total_hari_kerja_bulan++;
    }
}

// Kurangi dengan hari libur nasional
$total_hari_kerja_bulan -= $row_libur['total'];

// Hitung total hari kerja per bidang
$total_hari_kerja_per_bidang = [];
$first_day = mktime(0, 0, 0, $bulan, 1, $tahun);
$last_day = mktime(0, 0, 0, $bulan + 1, 0, $tahun);

// Ambil data hari libur untuk bulan ini
$sql_libur = "SELECT tanggal FROM hari_libur 
              WHERE MONTH(tanggal) = '$bulan' AND YEAR(tanggal) = '$tahun'";
$result_libur = mysqli_query($conn, $sql_libur);
$hari_libur = [];
while ($row_libur = mysqli_fetch_assoc($result_libur)) {
    $hari_libur[] = $row_libur['tanggal'];
}

// Ambil semua bidang
$query_bidang = "SELECT id FROM bidang";
$result_bidang = mysqli_query($conn, $query_bidang);
while ($row_bidang = mysqli_fetch_assoc($result_bidang)) {
    $bidang_id = $row_bidang['id'];
    $total_hari_kerja_bulan = 0;
    
    // Hitung hari kerja untuk setiap hari dalam bulan
    for ($i = $first_day; $i <= $last_day; $i += 86400) {
        $current_date = date('Y-m-d', $i);
        
        // Skip jika hari ini adalah hari libur
        if (in_array($current_date, $hari_libur)) {
            continue;
        }
        
        $hari = date('N', $i); // 1 (Senin) sampai 7 (Minggu)
        $sql_check = "SELECT status FROM hari_kerja 
                     WHERE bidang_id = '$bidang_id' 
                     AND hari = CASE 
                        WHEN $hari = 1 THEN 'Senin'
                        WHEN $hari = 2 THEN 'Selasa'
                        WHEN $hari = 3 THEN 'Rabu'
                        WHEN $hari = 4 THEN 'Kamis'
                        WHEN $hari = 5 THEN 'Jumat'
                        WHEN $hari = 6 THEN 'Sabtu'
                        WHEN $hari = 7 THEN 'Minggu'
                     END";
        $result_check = mysqli_query($conn, $sql_check);
        $row_check = mysqli_fetch_assoc($result_check);
        if ($row_check && $row_check['status'] == 1) {
            $total_hari_kerja_bulan++;
        }
    }
    
    $total_hari_kerja_per_bidang[$bidang_id] = $total_hari_kerja_bulan;
}

// Proses export ke Excel
if (isset($_GET['export']) && $_GET['export'] == 'excel') {
    // Set header untuk download file Excel
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="Riwayat_Tunjangan_IKK_Mamin_' . $bulan . '_' . $tahun . '.xls"');
    header('Cache-Control: max-age=0');

    // Ambil data tunjangan untuk bulan yang dipilih (SQL Query yang diperbarui dengan COALESCE, kecuali karyawan koperasi)
    $sql = "SELECT
                u.nik,
                u.nama,
                COALESCE(pt.ikk_bulanan, 0) as ikk_bulanan,
                COALESCE(pt.mamin_bulanan, 0) as mamin_bulanan,
                COALESCE(COUNT(CASE WHEN p.status = 'Tepat Waktu' THEN 1 END), 0) as tepat_waktu,
                COALESCE(COUNT(CASE WHEN p.status = 'Terlambat' THEN 1 END), 0) as terlambat,
                COALESCE(COUNT(CASE WHEN p.status = 'Pulang Awal' THEN 1 END), 0) as pulang_awal,
                COALESCE(COUNT(CASE WHEN p.status = 'Lembur' THEN 1 END), 0) as lembur,
                COALESCE(COUNT(p.id), 0) as total_kehadiran,
                b.id as bidang_id,
                b.nama_bidang
            FROM users u
            LEFT JOIN presensi p ON u.id = p.user_id
                AND MONTH(p.tanggal) = ?
                AND YEAR(p.tanggal) = ?
            LEFT JOIN pengaturan_tunjangan pt ON pt.bulan = ?
                AND pt.tahun = ?
            LEFT JOIN bidang b ON u.bidang_id = b.id
            WHERE u.role = 'karyawan' AND (u.keterangan != 'koperasi' OR u.keterangan IS NULL)
            GROUP BY u.id, u.nik, u.nama, ikk_bulanan, mamin_bulanan, b.id, b.nama_bidang";

    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        die("Error preparing statement: " . $conn->error);
    }

    $stmt->bind_param("ssss", $bulan, $tahun, $bulan, $tahun);
    $stmt->execute();
    $result = $stmt->get_result();

    // Output tabel Excel
    echo '<table border="1">';
    echo '<tr>';
    echo '<th colspan="8">RIWAYAT TUNJANGAN IKK & MAMIN</th>';
    echo '</tr>';
    echo '<tr>';
    echo '<th colspan="8">Periode: ' . date('F Y', strtotime($tahun . '-' . $bulan . '-01')) . '</th>';
    echo '</tr>';
    echo '<tr>';
    echo '<th>No</th>';
    echo '<th>NIK</th>';
    echo '<th>Nama Karyawan</th>';
    echo '<th>Jumlah Hari Kerja</th>';
    echo '<th>Total Denda</th>';
    echo '<th>Tunjangan IKK</th>';
    echo '<th>Tunjangan Mamin</th>';
    echo '<th>Total</th>';
    echo '</tr>';

    $no = 1;
    while ($row = $result->fetch_assoc()): 
        $bidang_id = $row['bidang_id'];
        $total_hari_kerja = isset($total_hari_kerja_per_bidang[$bidang_id]) ? 
                           $total_hari_kerja_per_bidang[$bidang_id] : 0;
        
        // Nilai langsung dari query (sudah di-COALESCE)
        $terlambat = $row['terlambat'];
        $pulang_awal = $row['pulang_awal'];
        $ikk_bulanan = $row['ikk_bulanan'];
        $mamin_bulanan = $row['mamin_bulanan'];
        $total_kehadiran = $row['total_kehadiran'];

        // Hitung denda berdasarkan keterlambatan dan pulang awal
        $total_denda = ($terlambat * $denda['denda_masuk']) + 
                      ($pulang_awal * $denda['denda_pulang']);
        
        // Hitung tunjangan berdasarkan proporsi kehadiran
        // Hindari pembagian dengan nol
        $total_ikk = ($total_hari_kerja > 0) ? ($ikk_bulanan / $total_hari_kerja) * $total_kehadiran : 0;
        $total_mamin = ($total_hari_kerja > 0) ? ($mamin_bulanan / $total_hari_kerja) * $total_kehadiran : 0;
        
        // Hitung total
        $total = $total_ikk + $total_mamin - $total_denda;
        
        echo '<tr>';
        echo '<td>' . $no++ . '</td>';
        echo '<td>' . $row['nik'] . '</td>';
        echo '<td>' . $row['nama'] . '</td>';
        echo '<td style="mso-number-format:\@">' . $total_hari_kerja . '</td>'; // Perbaikan format Excel di sini
        echo '<td>Rp ' . number_format($total_denda, 0, ',', '.') . '</td>';
        echo '<td>Rp ' . number_format($total_ikk, 0, ',', '.') . '</td>';
        echo '<td>Rp ' . number_format($total_mamin, 0, ',', '.') . '</td>';
        echo '<td>Rp ' . number_format($total, 0, ',', '.') . '</td>';
        echo '</tr>';
    endwhile;

    echo '</table>';
    exit;
}

// Ambil data tunjangan untuk bulan yang dipilih (SQL Query yang diperbarui dengan COALESCE, kecuali karyawan koperasi)
$sql = "SELECT
            u.nik,
            u.nama,
            COALESCE(pt.ikk_bulanan, 0) as ikk_bulanan,
            COALESCE(pt.mamin_bulanan, 0) as mamin_bulanan,
            COALESCE(COUNT(CASE WHEN p.status = 'Tepat Waktu' THEN 1 END), 0) as tepat_waktu,
            COALESCE(COUNT(CASE WHEN p.status = 'Terlambat' THEN 1 END), 0) as terlambat,
            COALESCE(COUNT(CASE WHEN p.status = 'Pulang Awal' THEN 1 END), 0) as pulang_awal,
            COALESCE(COUNT(CASE WHEN p.status = 'Lembur' THEN 1 END), 0) as lembur,
            COALESCE(COUNT(p.id), 0) as total_kehadiran,
            b.id as bidang_id,
            b.nama_bidang
        FROM users u
        LEFT JOIN presensi p ON u.id = p.user_id
            AND MONTH(p.tanggal) = ?
            AND YEAR(p.tanggal) = ?
        LEFT JOIN pengaturan_tunjangan pt ON pt.bulan = ?
            AND pt.tahun = ?
        LEFT JOIN bidang b ON u.bidang_id = b.id
        WHERE u.role = 'karyawan' AND (u.keterangan != 'koperasi' OR u.keterangan IS NULL)
        GROUP BY u.id, u.nik, u.nama, ikk_bulanan, mamin_bulanan, b.id, b.nama_bidang";

$stmt = $conn->prepare($sql);
if ($stmt === false) {
    die("Error preparing statement: " . $conn->error);
}

$stmt->bind_param("ssss", $bulan, $tahun, $bulan, $tahun);
$stmt->execute();
$result = $stmt->get_result();

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Riwayat Tunjangan IKK & MAMIN</h3>
                    <div class="card-tools">
                        <div class="btn-group">
                            <?php
                            // Hitung bulan dan tahun sebelumnya
                            $prev_month = date('m', strtotime("$tahun-$bulan-01 -1 month"));
                            $prev_year = date('Y', strtotime("$tahun-$bulan-01 -1 month"));
                            
                            // Hitung bulan dan tahun selanjutnya
                            $next_month = date('m', strtotime("$tahun-$bulan-01 +1 month"));
                            $next_year = date('Y', strtotime("$tahun-$bulan-01 +1 month"));
                            
                            // Cek apakah bulan selanjutnya melebihi bulan sekarang
                            $current_month = date('m');
                            $current_year = date('Y');
                            $is_next_month_allowed = ($next_year < $current_year) || 
                                                   ($next_year == $current_year && $next_month <= $current_month);
                            ?>
                            
                            <a href="?bulan=<?php echo $prev_month; ?>&tahun=<?php echo $prev_year; ?>" 
                               class="btn btn-default">
                                <i class="fas fa-chevron-left"></i> Bulan Sebelumnya
                            </a>
                            
                            <button type="button" class="btn btn-default" disabled>
                                <?php echo date('F Y', strtotime("$tahun-$bulan-01")); ?>
                            </button>
                            
                            <?php if ($is_next_month_allowed): ?>
                            <a href="?bulan=<?php echo $next_month; ?>&tahun=<?php echo $next_year; ?>" 
                               class="btn btn-default">
                                Bulan Selanjutnya <i class="fas fa-chevron-right"></i>
                            </a>
                            <?php endif; ?>

                            <a href="?bulan=<?php echo $bulan; ?>&tahun=<?php echo $tahun; ?>&export=excel" 
                               class="btn btn-success">
                                <i class="fas fa-file-excel"></i> Export Excel
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>NIK</th>
                                    <th>Nama</th>
                                    <th>Kehadiran</th>
                                    <th>Jumlah Hari Kerja</th>
                                    <th>Denda</th>
                                    <th>IKK</th>
                                    <th>MAMIN</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($row = $result->fetch_assoc()): 
                                    $bidang_id = $row['bidang_id'];
                                    $total_hari_kerja = isset($total_hari_kerja_per_bidang[$bidang_id]) ? 
                                                       $total_hari_kerja_per_bidang[$bidang_id] : 0;
                                    
                                    // Nilai langsung dari query (sudah di-COALESCE)
                                    $terlambat = $row['terlambat'];
                                    $pulang_awal = $row['pulang_awal'];
                                    $ikk_bulanan = $row['ikk_bulanan'];
                                    $mamin_bulanan = $row['mamin_bulanan'];
                                    $total_kehadiran = $row['total_kehadiran'];

                                    // Hitung denda berdasarkan keterlambatan dan pulang awal
                                    $total_denda = ($terlambat * $denda['denda_masuk']) + 
                                                  ($pulang_awal * $denda['denda_pulang']);
                                    
                                    // Hitung tunjangan berdasarkan proporsi kehadiran
                                    // Hindari pembagian dengan nol
                                    $total_ikk = ($total_hari_kerja > 0) ? ($ikk_bulanan / $total_hari_kerja) * $total_kehadiran : 0;
                                    $total_mamin = ($total_hari_kerja > 0) ? ($mamin_bulanan / $total_hari_kerja) * $total_kehadiran : 0;
                                    
                                    // Hitung total
                                    $total = $total_ikk + $total_mamin - $total_denda;
                                ?>
                                <tr>
                                    <td><?php echo $row['nik']; ?></td>
                                    <td><?php echo $row['nama']; ?></td>
                                    <td>
                                        Tepat Waktu: <?php echo isset($row['tepat_waktu']) ? $row['tepat_waktu'] : 0; ?><br>
                                        Terlambat: <?php echo isset($row['terlambat']) ? $row['terlambat'] : 0; ?><br>
                                        Pulang Awal: <?php echo isset($row['pulang_awal']) ? $row['pulang_awal'] : 0; ?><br>
                                        Lembur: <?php echo isset($row['lembur']) ? $row['lembur'] : 0; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php echo $total_kehadiran; ?> / <?php echo $total_hari_kerja; ?>
                                    </td>
                                    <td>Rp. <?php echo number_format($total_denda, 0, ',', '.'); ?></td>
                                    <td>Rp. <?php echo number_format($total_ikk, 0, ',', '.'); ?></td>
                                    <td>Rp. <?php echo number_format($total_mamin, 0, ',', '.'); ?></td>
                                    <td>Rp. <?php echo number_format($total, 0, ',', '.'); ?></td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?> 