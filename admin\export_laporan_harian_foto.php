<?php
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

checkAccess('admin');

$nama_karyawan = isset($_GET['nama_karyawan']) ? clean($_GET['nama_karyawan']) : '';
$periode = isset($_GET['periode']) ? clean($_GET['periode']) : '';

if (empty($nama_karyawan) || empty($periode)) {
    $_SESSION['error'] = 'Nama karyawan dan periode harus dipilih untuk export Excel!';
    header('Location: laporan_harian.php');
    exit;
}

// Set header untuk download file Excel dengan foto
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment;filename="Laporan_Kinerja_Harian_Foto_' . str_replace(' ', '_', $nama_karyawan) . '_' . $periode . '.xls"');
header('Cache-Control: max-age=0');

// Query untuk mengambil data berdasarkan filter
$query = "SELECT * FROM laporan_harian 
          WHERE nama_karyawan = '" . mysqli_real_escape_string($conn, $nama_karyawan) . "' 
          AND DATE_FORMAT(tanggal, '%Y-%m') = '" . mysqli_real_escape_string($conn, $periode) . "'
          ORDER BY tanggal ASC";
$result = mysqli_query($conn, $query);

// Fungsi untuk mengkonversi gambar ke base64
function getImageBase64($imagePath) {
    if (file_exists($imagePath)) {
        $imageData = file_get_contents($imagePath);
        $imageType = pathinfo($imagePath, PATHINFO_EXTENSION);
        $base64 = base64_encode($imageData);
        return 'data:image/' . $imageType . ';base64,' . $base64;
    }
    return null;
}

// Output HTML untuk Excel dengan foto embedded
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Laporan Kinerja Harian - <?php echo htmlspecialchars($nama_karyawan); ?></title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin-top: 20px; table-layout: fixed; }
        th, td { border: 1px solid #000; padding: 4px; text-align: left; vertical-align: middle; overflow: hidden; }
        th { background-color: #4CAF50; color: white; font-weight: bold; text-align: center; }
        .header { background-color: #2E7D32; color: white; text-align: center; font-size: 18px; font-weight: bold; padding: 15px; }
        .info { background-color: #E8F5E8; text-align: center; font-weight: bold; padding: 10px; }
        .foto-cell {
            width: 120px !important;
            height: 90px !important;
            text-align: center;
            padding: 2px !important;
            vertical-align: middle;
        }
        .foto-img {
            width: 110px !important;
            height: 80px !important;
            object-fit: cover;
            border: 1px solid #ddd;
            display: block;
            margin: 0 auto;
        }
        .no-foto {
            width: 110px;
            height: 80px;
            border: 1px dashed #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            background-color: #f9f9f9;
            font-size: 10px;
            margin: 0 auto;
        }
        .keterangan-cell { width: 250px; word-wrap: break-word; font-size: 11px; }
        .center { text-align: center; }
        .date-cell { white-space: nowrap; width: 80px; font-size: 11px; }
        .no-cell { width: 30px; text-align: center; }
        .periode-cell { width: 80px; text-align: center; font-size: 11px; }
        .file-cell { width: 100px; font-size: 10px; text-align: center; }
    </style>
</head>
<body>
    <table>
        <tr>
            <th colspan="7" class="header">LAPORAN KINERJA HARIAN</th>
        </tr>
        <tr>
            <td colspan="7" class="info">
                <strong>Nama Karyawan:</strong> <?php echo htmlspecialchars($nama_karyawan); ?>
            </td>
        </tr>
        <tr>
            <td colspan="7" class="info">
                <strong>Periode:</strong> <?php echo date('F Y', strtotime($periode . '-01')); ?>
            </td>
        </tr>
        <tr>
            <td colspan="7" class="info">
                <strong>Tanggal Export:</strong> <?php echo date('d F Y, H:i:s'); ?>
            </td>
        </tr>
        <tr>
            <th class="no-cell">No</th>
            <th class="date-cell">Tanggal</th>
            <th class="periode-cell">Periode</th>
            <th class="keterangan-cell">Keterangan</th>
            <th class="foto-cell">Foto</th>
            <th class="file-cell">File Foto</th>
            <th class="date-cell">Waktu Input</th>
        </tr>
        
        <?php
        $no = 1;
        if (mysqli_num_rows($result) > 0) {
            while ($row = mysqli_fetch_assoc($result)) {
                echo '<tr style="height: 90px;">';
                echo '<td class="no-cell">' . $no++ . '</td>';
                echo '<td class="date-cell">' . date('d/m/Y', strtotime($row['tanggal'])) . '</td>';
                echo '<td class="periode-cell">' . htmlspecialchars($row['periode']) . '</td>';
                echo '<td class="keterangan-cell">' . nl2br(htmlspecialchars($row['keterangan'])) . '</td>';

                // Kolom foto dengan ukuran tetap
                echo '<td class="foto-cell">';
                if ($row['foto'] && file_exists('../uploads/' . $row['foto'])) {
                    $imagePath = '../uploads/' . $row['foto'];
                    $base64Image = getImageBase64($imagePath);
                    if ($base64Image) {
                        echo '<img src="' . $base64Image . '" alt="Foto" class="foto-img" />';
                    } else {
                        echo '<div class="no-foto">Error Load</div>';
                    }
                } else {
                    echo '<div class="no-foto">No Photo</div>';
                }
                echo '</td>';

                // Kolom nama file foto
                echo '<td class="file-cell">';
                if ($row['foto']) {
                    echo '<div style="font-size: 9px;">' . htmlspecialchars($row['foto']) . '</div>';
                    echo '<div style="font-size: 8px; color: #666;">' . (file_exists('../uploads/' . $row['foto']) ? '✓ Ada' : '✗ Hilang') . '</div>';
                } else {
                    echo '<em style="color: #999; font-size: 9px;">No File</em>';
                }
                echo '</td>';

                echo '<td class="date-cell">' . date('d/m/Y<br/>H:i', strtotime($row['created_at'])) . '</td>';
                echo '</tr>';
            }
        } else {
            echo '<tr>';
            echo '<td colspan="7" style="text-align: center; padding: 30px; color: #666; font-style: italic;">';
            echo 'Tidak ada data laporan untuk karyawan <strong>' . htmlspecialchars($nama_karyawan) . '</strong> pada periode <strong>' . date('F Y', strtotime($periode . '-01')) . '</strong>';
            echo '</td>';
            echo '</tr>';
        }
        ?>
        
        <tr>
            <td colspan="7" style="text-align: center; padding: 15px; background-color: #f5f5f5; font-size: 12px; color: #666;">
                <em>Laporan ini digenerate otomatis oleh Sistem Absensi pada <?php echo date('d F Y, H:i:s'); ?></em>
            </td>
        </tr>
    </table>
</body>
</html>
