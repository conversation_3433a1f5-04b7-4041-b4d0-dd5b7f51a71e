<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Inisialisasi variabel
$tanggal = isset($_GET['tanggal']) ? $_GET['tanggal'] : (isset($_POST['tanggal']) ? $_POST['tanggal'] : date('Y-m-d'));
$tanggal_akhir = isset($_POST['tanggal_akhir']) ? $_POST['tanggal_akhir'] : '';
$mode_rentang = isset($_POST['mode_rentang']) ? $_POST['mode_rentang'] : 'single';
$jam_masuk = isset($_POST['jam_masuk']) ? $_POST['jam_masuk'] : '07:05';
$jam_pulang = isset($_POST['jam_pulang']) ? $_POST['jam_pulang'] : '15:32';
$status = isset($_POST['status']) ? $_POST['status'] : 'Tepat Waktu';
$keterangan = isset($_POST['keterangan']) ? $_POST['keterangan'] : 'Absensi manual oleh admin';
$selected_karyawan = isset($_POST['karyawan']) ? $_POST['karyawan'] : [];
$skip_weekend = isset($_POST['skip_weekend']) ? true : false;

// Ambil semua data karyawan
$query = "SELECT id, nik, nama, bidang FROM users WHERE role = 'karyawan' ORDER BY nama ASC";
$result = mysqli_query($conn, $query);
$karyawan_list = [];
while ($row = mysqli_fetch_assoc($result)) {
    $karyawan_list[] = $row;
}

// Cek karyawan yang sudah absen pada tanggal tersebut
$query = "SELECT user_id FROM presensi WHERE tanggal = '$tanggal'";
$result = mysqli_query($conn, $query);
$karyawan_sudah_absen = [];
while ($row = mysqli_fetch_assoc($result)) {
    $karyawan_sudah_absen[] = $row['user_id'];
}

// Fungsi untuk upload foto
function uploadFoto($file, $prefix) {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return null;
    }

    // Validasi ukuran file (maksimal 2MB)
    if ($file['size'] > 2 * 1024 * 1024) {
        return false;
    }

    // Validasi tipe file
    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!in_array($file['type'], $allowed_types)) {
        return false;
    }

    // Buat nama file unik
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = $prefix . '_' . date('Ymd_His') . '_' . uniqid() . '.' . $extension;

    // Buat direktori jika belum ada
    $upload_dir = '../uploads/manual_absensi/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }

    // Upload file
    $target_path = $upload_dir . $filename;
    if (move_uploaded_file($file['tmp_name'], $target_path)) {
        return 'uploads/manual_absensi/' . $filename;
    }

    return false;
}

// Proses form absensi manual
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['submit'])) {
    // Validasi input
    if (empty($tanggal) || empty($jam_masuk) || empty($status) || empty($selected_karyawan)) {
        setMessage('danger', 'Semua field wajib diisi!');
    } else if ($mode_rentang == 'range' && empty($tanggal_akhir)) {
        setMessage('danger', 'Tanggal akhir wajib diisi untuk mode rentang tanggal!');
    } else if ($mode_rentang == 'range' && $tanggal_akhir < $tanggal) {
        setMessage('danger', 'Tanggal akhir tidak boleh lebih kecil dari tanggal awal!');
    } else {
        // Proses upload foto
        $foto_masuk_path = null;
        $foto_pulang_path = null;
        $upload_error = false;

        if (isset($_FILES['foto_masuk']) && $_FILES['foto_masuk']['error'] !== UPLOAD_ERR_NO_FILE) {
            $foto_masuk_path = uploadFoto($_FILES['foto_masuk'], 'masuk');
            if ($foto_masuk_path === false) {
                setMessage('danger', 'Gagal upload foto masuk. Pastikan file berformat JPG/PNG dan ukuran maksimal 2MB.');
                $upload_error = true;
            }
        }

        if (isset($_FILES['foto_pulang']) && $_FILES['foto_pulang']['error'] !== UPLOAD_ERR_NO_FILE) {
            $foto_pulang_path = uploadFoto($_FILES['foto_pulang'], 'pulang');
            if ($foto_pulang_path === false) {
                setMessage('danger', 'Gagal upload foto pulang. Pastikan file berformat JPG/PNG dan ukuran maksimal 2MB.');
                $upload_error = true;
            }
        }

        if ($upload_error) {
            // Jangan lanjutkan jika ada error upload
        } else {
            // Cek apakah file gambar dummy ada (fallback jika tidak ada foto yang diupload)
            if (!$foto_masuk_path && !file_exists('../uploads/manual_absensi.jpg')) {
                // Jika tidak ada, buat pesan error
            setMessage('danger', 'File gambar dummy untuk absensi manual tidak ditemukan. Silakan hubungi administrator.');
            redirect('admin/absensi_manual.php');
            exit;
        }

        $success_count = 0;
        $error_count = 0;
        $total_days = 0;

        // Buat array tanggal yang akan diproses
        $tanggal_list = [];
        if ($mode_rentang == 'range') {
            $start_date = new DateTime($tanggal);
            $end_date = new DateTime($tanggal_akhir);

            while ($start_date <= $end_date) {
                $current_date = $start_date->format('Y-m-d');
                $day_of_week = $start_date->format('w'); // 0 = Sunday, 6 = Saturday

                // Skip weekend jika opsi dicentang
                if ($skip_weekend && ($day_of_week == 0 || $day_of_week == 6)) {
                    $start_date->add(new DateInterval('P1D'));
                    continue;
                }

                $tanggal_list[] = $current_date;
                $start_date->add(new DateInterval('P1D'));
            }
        } else {
            $tanggal_list[] = $tanggal;
        }

        $total_days = count($tanggal_list);

        // Loop untuk setiap tanggal
        foreach ($tanggal_list as $current_tanggal) {
            // Loop untuk setiap karyawan yang dipilih
            foreach ($selected_karyawan as $karyawan_id) {
                // Cek apakah karyawan sudah absen pada tanggal tersebut
                $query = "SELECT * FROM presensi WHERE user_id = '$karyawan_id' AND tanggal = '$current_tanggal'";
                $result = mysqli_query($conn, $query);

                if (mysqli_num_rows($result) > 0) {
                    // Jika sudah ada, update data
                    $presensi = mysqli_fetch_assoc($result);
                    $presensi_id = $presensi['id'];

                    $query = "UPDATE presensi SET
                              jam_masuk = '$jam_masuk',
                              jam_pulang = " . (!empty($jam_pulang) ? "'$jam_pulang'" : "NULL") . ",
                              status = '$status',
                              keterangan = '$keterangan'";

                    // Update foto jika ada yang diupload
                    if ($foto_masuk_path) {
                        $query .= ", foto_masuk = '$foto_masuk_path'";
                    }
                    if ($foto_pulang_path) {
                        $query .= ", foto_pulang = '$foto_pulang_path'";
                    }

                    $query .= " WHERE id = '$presensi_id'";
                } else {
                    // Jika belum ada, insert data baru
                    $foto_masuk_value = $foto_masuk_path ? "'$foto_masuk_path'" : "'manual_absensi.jpg'";
                    $foto_pulang_value = $foto_pulang_path ? "'$foto_pulang_path'" : "NULL";

                    $query = "INSERT INTO presensi (user_id, tanggal, jam_masuk, jam_pulang, foto_masuk, foto_pulang, lokasi_masuk, status, keterangan)
                              VALUES ('$karyawan_id', '$current_tanggal', '$jam_masuk', " .
                              (!empty($jam_pulang) ? "'$jam_pulang'" : "NULL") .
                              ", $foto_masuk_value, $foto_pulang_value, 'Absensi Manual', '$status', '$keterangan')";
                }

                if (mysqli_query($conn, $query)) {
                    $success_count++;
                } else {
                    $error_count++;
                    // Log error untuk debugging
                    error_log("Error absensi manual: " . mysqli_error($conn) . " Query: " . $query);
                }
            }
        }

        $karyawan_count = count($selected_karyawan);
        if ($success_count > 0) {
            if ($mode_rentang == 'range') {
                setMessage('success', "Berhasil mengatur absensi untuk $karyawan_count karyawan selama $total_days hari (total $success_count entri)" . ($error_count > 0 ? ", gagal untuk $error_count entri" : ""));
            } else {
                setMessage('success', "Berhasil mengatur absensi untuk $success_count karyawan" . ($error_count > 0 ? ", gagal untuk $error_count karyawan" : ""));
            }
        } else {
            setMessage('danger', "Gagal mengatur absensi untuk semua karyawan yang dipilih");
        }

        // Redirect ke halaman monitoring dengan tanggal yang dipilih
        redirect('admin/monitoring.php?tanggal=' . $tanggal);
        }
    }
}

// Include header
include_once '../includes/header.php';

// Ambil pesan jika ada
$message = getMessage();
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Absensi Manual</h1>
        <a href="monitoring.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <?php if ($message): ?>
    <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show" role="alert">
        <?php echo $message['text']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Fitur Absensi Manual:</strong><br>
        • <strong>Tanggal Tunggal:</strong> Input absensi untuk satu hari tertentu<br>
        • <strong>Rentang Tanggal:</strong> Input absensi untuk beberapa hari sekaligus dengan opsi melewati akhir pekan<br>
        • Pilih karyawan yang ingin diabsenkan dan atur jam serta status absensi
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Form Absensi Manual</h6>
        </div>
        <div class="card-body">
            <form method="post" action="" enctype="multipart/form-data">
                <!-- Mode Tanggal -->
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Mode Tanggal</label>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="mode_rentang" id="mode_single" value="single" <?php echo $mode_rentang == 'single' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="mode_single">
                                    Tanggal Tunggal
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="mode_rentang" id="mode_range" value="range" <?php echo $mode_rentang == 'range' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="mode_range">
                                    Rentang Tanggal
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Input Tanggal -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="tanggal" id="label_tanggal">Tanggal Absensi</label>
                            <input type="date" class="form-control" id="tanggal" name="tanggal" value="<?php echo $tanggal; ?>" required>
                            <small class="form-text text-muted" id="help_tanggal">Perubahan tanggal akan memperbarui data karyawan yang sudah absen</small>
                        </div>
                    </div>
                    <div class="col-md-4" id="tanggal_akhir_group" style="display: none;">
                        <div class="form-group">
                            <label for="tanggal_akhir">Tanggal Akhir</label>
                            <input type="date" class="form-control" id="tanggal_akhir" name="tanggal_akhir" value="<?php echo $tanggal_akhir; ?>">
                            <small class="form-text text-muted">Tanggal akhir rentang absensi</small>
                        </div>
                    </div>
                    <div class="col-md-4" id="skip_weekend_group" style="display: none;">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="skip_weekend" id="skip_weekend" <?php echo $skip_weekend ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="skip_weekend">
                                    Lewati Akhir Pekan (Sabtu & Minggu)
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Jam Kerja -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="jam_masuk">Jam Masuk</label>
                            <input type="time" class="form-control" id="jam_masuk" name="jam_masuk" value="<?php echo $jam_masuk; ?>" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="jam_pulang">Jam Pulang (Opsional)</label>
                            <input type="time" class="form-control" id="jam_pulang" name="jam_pulang" value="<?php echo $jam_pulang; ?>">
                        </div>
                    </div>
                </div>

                <!-- Upload Foto -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="foto_masuk">Foto Absen Masuk (Opsional)</label>
                            <input type="file" class="form-control" id="foto_masuk" name="foto_masuk" accept="image/*">
                            <small class="form-text text-muted">Format: JPG, PNG, JPEG. Maksimal 2MB</small>
                            <div id="preview_masuk" class="mt-2" style="display: none;">
                                <img id="img_preview_masuk" src="" alt="Preview" style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="foto_pulang">Foto Absen Pulang (Opsional)</label>
                            <input type="file" class="form-control" id="foto_pulang" name="foto_pulang" accept="image/*">
                            <small class="form-text text-muted">Format: JPG, PNG, JPEG. Maksimal 2MB</small>
                            <div id="preview_pulang" class="mt-2" style="display: none;">
                                <img id="img_preview_pulang" src="" alt="Preview" style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status" required>
                                <option value="Tepat Waktu" <?php echo $status == 'Tepat Waktu' ? 'selected' : ''; ?>>Tepat Waktu</option>
                                <option value="Terlambat" <?php echo $status == 'Terlambat' ? 'selected' : ''; ?>>Terlambat</option>
                                <option value="Pulang Awal" <?php echo $status == 'Pulang Awal' ? 'selected' : ''; ?>>Pulang Awal</option>
                                <option value="Lembur" <?php echo $status == 'Lembur' ? 'selected' : ''; ?>>Lembur</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="keterangan">Keterangan</label>
                            <input type="text" class="form-control" id="keterangan" name="keterangan" value="<?php echo $keterangan; ?>" required>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-3">
                    <label>Pilih Karyawan</label>
                    <div class="d-flex justify-content-between mb-2">
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-primary" id="selectAll">Pilih Semua</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAll">Batalkan Semua</button>
                        </div>
                        <div class="input-group" style="max-width: 300px;">
                            <input type="text" class="form-control form-control-sm" id="searchKaryawan" placeholder="Cari karyawan...">
                            <button class="btn btn-sm btn-outline-secondary" type="button" id="clearSearch">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th width="50">Pilih</th>
                                    <th>NIK</th>
                                    <th>Nama</th>
                                    <th>Bidang</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($karyawan_list)): ?>
                                    <tr>
                                        <td colspan="4" class="text-center">Tidak ada data karyawan</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($karyawan_list as $k): ?>
                                        <tr>
                                            <td class="text-center">
                                                <div class="form-check">
                                                    <input class="form-check-input karyawan-checkbox" type="checkbox" name="karyawan[]" value="<?php echo $k['id']; ?>" id="karyawan_<?php echo $k['id']; ?>">
                                                </div>
                                            </td>
                                            <td><?php echo $k['nik']; ?></td>
                                            <td>
                                                <?php echo $k['nama']; ?>
                                                <?php if (in_array($k['id'], $karyawan_sudah_absen)): ?>
                                                    <span class="badge bg-success">Sudah Absen</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $k['bidang'] ?? '-'; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="text-center">
                    <button type="submit" name="submit" class="btn btn-primary" id="submitBtn" disabled>
                        <i class="fas fa-save"></i> Simpan Absensi
                    </button>
                    <div class="mt-2 text-danger" id="validationMessage">
                        Pilih minimal satu karyawan untuk melanjutkan
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const submitBtn = document.getElementById('submitBtn');
        const validationMessage = document.getElementById('validationMessage');
        const checkboxes = document.querySelectorAll('.karyawan-checkbox');

        // Fungsi untuk memilih/membatalkan semua checkbox
        document.getElementById('selectAll').addEventListener('click', function() {
            checkboxes.forEach(function(checkbox) {
                checkbox.checked = true;
            });
            validateForm();
        });

        document.getElementById('deselectAll').addEventListener('click', function() {
            checkboxes.forEach(function(checkbox) {
                checkbox.checked = false;
            });
            validateForm();
        });

        // Tambahkan event listener untuk setiap checkbox
        checkboxes.forEach(function(checkbox) {
            checkbox.addEventListener('change', validateForm);
        });

        // Tambahkan event listener untuk validasi tanggal
        document.getElementById('tanggal').addEventListener('change', validateForm);
        document.getElementById('tanggal_akhir').addEventListener('change', validateForm);
        document.getElementById('mode_single').addEventListener('change', validateForm);
        document.getElementById('mode_range').addEventListener('change', validateForm);

        // Fungsi untuk validasi form
        function validateForm() {
            const checkedBoxes = document.querySelectorAll('.karyawan-checkbox:checked');
            const modeRange = document.getElementById('mode_range').checked;
            const tanggalAwal = document.getElementById('tanggal').value;
            const tanggalAkhir = document.getElementById('tanggal_akhir').value;

            let isValid = true;
            let message = '';

            // Validasi karyawan dipilih
            if (checkedBoxes.length === 0) {
                isValid = false;
                message = 'Pilih minimal satu karyawan untuk melanjutkan';
            }
            // Validasi rentang tanggal
            else if (modeRange && (!tanggalAkhir || tanggalAkhir < tanggalAwal)) {
                isValid = false;
                message = 'Tanggal akhir harus diisi dan tidak boleh lebih kecil dari tanggal awal';
            }

            if (isValid) {
                submitBtn.disabled = false;
                validationMessage.style.display = 'none';
            } else {
                submitBtn.disabled = true;
                validationMessage.textContent = message;
                validationMessage.style.display = 'block';
            }
        }

        // Validasi form saat halaman dimuat
        validateForm();

        // Fungsi pencarian karyawan
        const searchInput = document.getElementById('searchKaryawan');
        const clearSearchBtn = document.getElementById('clearSearch');
        const karyawanRows = document.querySelectorAll('tbody tr');

        // Fungsi untuk mengontrol tampilan mode tanggal
        function toggleDateMode() {
            const modeRange = document.getElementById('mode_range').checked;
            const tanggalAkhirGroup = document.getElementById('tanggal_akhir_group');
            const skipWeekendGroup = document.getElementById('skip_weekend_group');
            const labelTanggal = document.getElementById('label_tanggal');
            const helpTanggal = document.getElementById('help_tanggal');
            const tanggalAkhirInput = document.getElementById('tanggal_akhir');

            if (modeRange) {
                tanggalAkhirGroup.style.display = 'block';
                skipWeekendGroup.style.display = 'block';
                labelTanggal.textContent = 'Tanggal Awal';
                helpTanggal.textContent = 'Tanggal mulai rentang absensi';
                tanggalAkhirInput.required = true;
            } else {
                tanggalAkhirGroup.style.display = 'none';
                skipWeekendGroup.style.display = 'none';
                labelTanggal.textContent = 'Tanggal Absensi';
                helpTanggal.textContent = 'Perubahan tanggal akan memperbarui data karyawan yang sudah absen';
                tanggalAkhirInput.required = false;
            }
        }

        // Event listener untuk mode tanggal
        document.getElementById('mode_single').addEventListener('change', toggleDateMode);
        document.getElementById('mode_range').addEventListener('change', toggleDateMode);

        // Inisialisasi tampilan berdasarkan mode yang dipilih
        toggleDateMode();

        // Fungsi untuk memperbarui halaman saat tanggal berubah (hanya untuk mode single)
        const tanggalInput = document.getElementById('tanggal');
        tanggalInput.addEventListener('change', function() {
            // Hanya refresh jika mode single
            if (document.getElementById('mode_single').checked) {
                // Buat form untuk submit
                const form = document.createElement('form');
                form.method = 'GET';
                form.action = window.location.href;

                // Tambahkan input tanggal
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'tanggal';
                input.value = this.value;

                // Tambahkan input ke form dan submit
                form.appendChild(input);
                document.body.appendChild(form);
                form.submit();
            }
        });



        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            filterKaryawan(searchTerm);
        });

        clearSearchBtn.addEventListener('click', function() {
            searchInput.value = '';
            filterKaryawan('');
        });

        function filterKaryawan(searchTerm) {
            karyawanRows.forEach(function(row) {
                const nik = row.cells[1].textContent.toLowerCase();
                const nama = row.cells[2].textContent.toLowerCase();
                const bidang = row.cells[3].textContent.toLowerCase();

                if (nik.includes(searchTerm) || nama.includes(searchTerm) || bidang.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
    });

    // Preview foto masuk
    document.getElementById('foto_masuk').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Validasi ukuran file (2MB)
            if (file.size > 2 * 1024 * 1024) {
                alert('Ukuran file terlalu besar. Maksimal 2MB.');
                this.value = '';
                document.getElementById('preview_masuk').style.display = 'none';
                return;
            }

            // Validasi tipe file
            if (!file.type.match('image.*')) {
                alert('File harus berupa gambar.');
                this.value = '';
                document.getElementById('preview_masuk').style.display = 'none';
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('img_preview_masuk').src = e.target.result;
                document.getElementById('preview_masuk').style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            document.getElementById('preview_masuk').style.display = 'none';
        }
    });

    // Preview foto pulang
    document.getElementById('foto_pulang').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Validasi ukuran file (2MB)
            if (file.size > 2 * 1024 * 1024) {
                alert('Ukuran file terlalu besar. Maksimal 2MB.');
                this.value = '';
                document.getElementById('preview_pulang').style.display = 'none';
                return;
            }

            // Validasi tipe file
            if (!file.type.match('image.*')) {
                alert('File harus berupa gambar.');
                this.value = '';
                document.getElementById('preview_pulang').style.display = 'none';
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('img_preview_pulang').src = e.target.result;
                document.getElementById('preview_pulang').style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            document.getElementById('preview_pulang').style.display = 'none';
        }
    });
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
