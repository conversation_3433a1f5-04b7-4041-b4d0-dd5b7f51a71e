# Fitur Export Excel Laporan Ki<PERSON><PERSON> (Dengan Foto)

## Deskripsi
Fitur ini memungkinkan admin untuk mengexport laporan kinerja harian karyawan berdasarkan nama karyawan dan periode bulan tertentu ke dalam berbagai format Excel, termasuk opsi untuk menampilkan foto dalam file export.

## Lokasi File
- **File Utama**: `admin/laporan_harian.php`
- **Export Standar**: `admin/laporan_harian.php` (parameter export=excel)
- **Export dengan Foto**: `admin/export_laporan_harian_foto.php`
- **Export XLSX Professional**: `admin/export_laporan_harian_xlsx.php`
- **File SQL**: `sql/laporan_harian.sql`

## Cara Penggunaan

### 1. Aks<PERSON> Hari<PERSON>
- Login sebagai admin
- Navigasi ke menu "Lap. Kinerja <PERSON>" di sidebar

### 2. Filter dan Export
1. **Pilih <PERSON>**: Gunakan dropdown "Filter <PERSON>" untuk memilih karyawan yang diinginkan
2. **Pilih Periode**: Gunakan dropdown "Filter Periode Bulan" untuk memilih bulan dan tahun
3. **Pilih Jenis Export**: Setelah kedua filter dipilih, pilih salah satu dari 3 opsi export:
   - **Export XLS (Standar)**: Format Excel sederhana tanpa foto
   - **Export HTML (Dengan Foto)**: Format HTML yang menampilkan foto embedded
   - **Export XLSX (Professional)**: Format Excel XML dengan informasi foto
4. **Download**: Klik tombol export yang diinginkan untuk mendownload file

## Fitur yang Ditambahkan

### 1. Filter Export
- Dropdown nama karyawan (berdasarkan data yang ada di tabel laporan_harian)
- Dropdown periode bulan-tahun (berdasarkan tanggal laporan yang ada)
- Tombol export yang hanya aktif jika kedua filter dipilih

### 2. Validasi
- Validasi bahwa nama karyawan dan periode harus dipilih sebelum export
- Pesan error jika filter tidak lengkap
- Redirect dengan pesan error jika parameter tidak valid

### 3. Format Export Excel

#### A. Export XLS (Standar)
- Header dengan judul "LAPORAN KINERJA HARIAN"
- Informasi nama karyawan dan periode
- Kolom: No, Tanggal, Periode, Keterangan, Foto, Link Foto, Waktu Input
- Format tanggal yang user-friendly (dd/mm/yyyy)
- Nama file: `Laporan_Kinerja_Harian_{nama_karyawan}_{periode}.xls`

#### B. Export HTML (Dengan Foto)
- Format HTML yang dapat dibuka di Excel
- Foto ditampilkan sebagai gambar embedded (base64)
- Styling yang lebih menarik dengan CSS
- Kolom foto menampilkan gambar aktual dengan ukuran 140x90px
- Nama file: `Laporan_Kinerja_Harian_Foto_{nama_karyawan}_{periode}.xls`

#### C. Export XLSX (Professional)
- Format Excel XML yang lebih modern
- Styling profesional dengan warna dan border
- Informasi foto dalam comment cell
- Kompatibilitas tinggi dengan Microsoft Excel
- Nama file: `Laporan_Kinerja_Harian_{nama_karyawan}_{periode}.xlsx`

### 4. JavaScript Enhancement
- Auto-enable/disable tombol export berdasarkan filter
- Validasi client-side sebelum redirect
- URL encoding untuk parameter yang aman

## Struktur Tabel laporan_harian
```sql
CREATE TABLE laporan_harian (
  id int(11) NOT NULL AUTO_INCREMENT,
  nama_karyawan varchar(100) NOT NULL,
  periode varchar(20) NOT NULL,
  tanggal date NOT NULL,
  keterangan text NOT NULL,
  foto varchar(255) DEFAULT NULL,
  created_at timestamp NOT NULL DEFAULT current_timestamp(),
  updated_at timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (id)
);
```

## Contoh Penggunaan
1. Admin memilih "John Doe" dari dropdown nama karyawan
2. Admin memilih "Januari 2025" dari dropdown periode
3. Admin klik tombol "Export Excel"
4. File `Laporan_Kinerja_Harian_John_Doe_2025-01.xls` akan terdownload
5. File berisi semua laporan harian John Doe untuk bulan Januari 2025

## Keamanan
- Menggunakan `mysqli_real_escape_string()` untuk mencegah SQL injection
- Menggunakan `htmlspecialchars()` untuk mencegah XSS
- Validasi parameter sebelum proses export
- Menggunakan fungsi `clean()` untuk sanitasi input

## Kompatibilitas

### Export XLS (Standar)
- Compatible dengan Microsoft Excel
- Compatible dengan LibreOffice Calc
- Compatible dengan Google Sheets (import)
- Format .xls untuk kompatibilitas maksimal

### Export HTML (Dengan Foto)
- Dapat dibuka di Microsoft Excel (akan menampilkan foto)
- Dapat dibuka di browser web
- Foto embedded sebagai base64 (tidak memerlukan file terpisah)
- Ukuran file lebih besar karena foto embedded

### Export XLSX (Professional)
- Optimal untuk Microsoft Excel 2007+
- Compatible dengan LibreOffice Calc
- Format XML yang lebih modern dan fleksibel
- Informasi foto tersimpan dalam metadata

## Catatan Teknis Foto

### Cara Kerja Foto di Export
1. **Export XLS**: Menampilkan nama file foto dan link
2. **Export HTML**: Foto dikonversi ke base64 dan ditampilkan langsung
3. **Export XLSX**: Informasi foto disimpan dalam comment cell

### Persyaratan Foto
- Foto harus tersimpan di folder `uploads/`
- Format yang didukung: JPG, PNG, GIF
- Ukuran maksimal foto di export HTML: 140x90px (auto-resize)
- Jika file foto tidak ditemukan, akan ditampilkan pesan "Tidak Ada Foto"

### Troubleshooting Foto
- Jika foto tidak muncul di export HTML, periksa path file di folder uploads
- Pastikan permission folder uploads dapat dibaca oleh web server
- Untuk file besar, export HTML mungkin memerlukan waktu lebih lama
