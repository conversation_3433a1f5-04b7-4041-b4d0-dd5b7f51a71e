<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Terima data JSON
$data = json_decode(file_get_contents('php://input'), true);

if (!$data) {
    echo json_encode(['success' => false, 'message' => 'Data tidak valid']);
    exit;
}

$id = $data['id'] ?? 0;
$type = $data['type'] ?? '';
$approver_id = $data['approver_id'] ?? 0;

if (!$id || !$type || !$approver_id) {
    echo json_encode(['success' => false, 'message' => 'Data tidak lengkap']);
    exit;
}

// Proses penolakan berdasarkan tipe
if ($type === 'gangguan_absensi') {
    $sql = "UPDATE gangguan_absensi 
            SET status = 'Rejected', 
                approved_at = NOW(), 
                approver_id = ? 
            WHERE id = ?";
} else if ($type === 'izin_dinas') {
    $sql = "UPDATE izin_dinas 
            SET status = 'Rejected', 
                approved_at = NOW(), 
                approver_id = ? 
            WHERE id = ?";
} else {
    echo json_encode(['success' => false, 'message' => 'Tipe pengajuan tidak valid']);
    exit;
}

$stmt = $conn->prepare($sql);
if ($stmt === false) {
    echo json_encode(['success' => false, 'message' => 'Gagal mempersiapkan query']);
    exit;
}

$stmt->bind_param("ii", $approver_id, $id);
if ($stmt->execute()) {
    echo json_encode(['success' => true, 'message' => 'Pengajuan berhasil ditolak']);
} else {
    echo json_encode(['success' => false, 'message' => 'Gagal menolak pengajuan']);
} 