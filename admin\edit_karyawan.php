<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Cek akses
checkAccess('admin');

// Cek apakah ada parameter id
if (!isset($_GET['id'])) {
    setMessage('danger', 'ID karyawan tidak valid');
    redirect('karyawan.php');
}

$id = $_GET['id'];

// Ambil data karyawan berdasarkan id
$query = "SELECT u.*, l.nama_lokasi, b.nama_bidang
          FROM users u
          LEFT JOIN lokasi l ON u.lokasi_id = l.id
          LEFT JOIN bidang b ON u.bidang_id = b.id
          WHERE u.id = '$id' AND u.role = 'karyawan'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('danger', 'Karyawan tidak ditemukan');
    redirect('karyawan.php');
}

$karyawan = mysqli_fetch_assoc($result);

// Proses edit karyawan
if (isset($_POST['edit'])) {
    $nik = $_POST['nik'];
    $nama = $_POST['nama'];
    $bidang_id = $_POST['bidang_id'];
    $jabatan = $_POST['jabatan'];
    $lokasi_id = $_POST['lokasi_id'];
    $keterangan = $_POST['keterangan'];

    // Validasi input
    if (empty($nik) || empty($nama) || empty($bidang_id) || empty($jabatan) || empty($lokasi_id)) {
        setMessage('danger', 'Semua field harus diisi');
        redirect('edit_karyawan.php?id=' . $id);
    }

    // Cek apakah NIK sudah digunakan oleh karyawan lain
    $query = "SELECT * FROM users WHERE nik = '$nik' AND id != '$id'";
    $result = mysqli_query($conn, $query);

    if (mysqli_num_rows($result) > 0) {
        setMessage('danger', 'NIK sudah digunakan oleh karyawan lain');
        redirect('edit_karyawan.php?id=' . $id);
    }

    // Upload foto profil jika ada
    $foto_profil = $karyawan['foto_profil'];

    if ($_FILES['foto_profil']['size'] > 0) {
        $target_dir = "../uploads/";
        $file_extension = pathinfo($_FILES["foto_profil"]["name"], PATHINFO_EXTENSION);
        $new_filename = time() . '_' . $id . '.' . $file_extension;
        $target_file = $target_dir . $new_filename;

        // Cek ukuran file (max 2MB)
        if ($_FILES["foto_profil"]["size"] > 2000000) {
            setMessage('danger', 'Ukuran file terlalu besar (maksimal 2MB)');
            redirect('edit_karyawan.php?id=' . $id);
        }

        // Cek tipe file (hanya gambar)
        $allowed_types = array('jpg', 'jpeg', 'png', 'gif');
        if (!in_array(strtolower($file_extension), $allowed_types)) {
            setMessage('danger', 'Hanya file gambar yang diperbolehkan (JPG, JPEG, PNG, GIF)');
            redirect('edit_karyawan.php?id=' . $id);
        }

        // Upload file
        if (move_uploaded_file($_FILES["foto_profil"]["tmp_name"], $target_file)) {
            // Hapus foto lama jika ada
            if (!empty($karyawan['foto_profil']) && file_exists($target_dir . $karyawan['foto_profil'])) {
                unlink($target_dir . $karyawan['foto_profil']);
            }

            $foto_profil = $new_filename;
        } else {
            setMessage('danger', 'Gagal mengupload file');
            redirect('edit_karyawan.php?id=' . $id);
        }
    }

    // Ambil nama bidang
    $nama_bidang = null;
    $query_bidang = "SELECT nama_bidang FROM bidang WHERE id = '$bidang_id'";
    $result_bidang = mysqli_query($conn, $query_bidang);
    if ($result_bidang && mysqli_num_rows($result_bidang) > 0) {
        $bidang_data = mysqli_fetch_assoc($result_bidang);
        $nama_bidang = $bidang_data['nama_bidang'];
    }

    // Cek apakah checkbox allow_barcode dicentang
    $allow_barcode = isset($_POST['allow_barcode']) ? 1 : 0;

    // Update data karyawan
    $query = "UPDATE users SET
              nik = '$nik',
              nama = '$nama',
              bidang_id = '$bidang_id',
              bidang = " . ($nama_bidang ? "'$nama_bidang'" : "NULL") . ",
              jabatan = '$jabatan',
              keterangan = '$keterangan',
              lokasi_id = '$lokasi_id',
              foto_profil = '$foto_profil',
              allow_barcode = '$allow_barcode'
              WHERE id = '$id'";

    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Karyawan berhasil diupdate');
        redirect('admin/karyawan.php');
    } else {
        setMessage('danger', 'Gagal mengupdate karyawan: ' . mysqli_error($conn));
        redirect('edit_karyawan.php?id=' . $id);
    }
}

// Ambil data lokasi untuk dropdown
$query = "SELECT * FROM lokasi ORDER BY nama_lokasi ASC";
$result = mysqli_query($conn, $query);

$lokasi = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $lokasi[] = $row;
    }
}

// Ambil data bidang untuk dropdown
$query = "SELECT * FROM bidang ORDER BY nama_bidang ASC";
$result = mysqli_query($conn, $query);

$bidang_list = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $bidang_list[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Edit Karyawan</h1>
        <a href="karyawan.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Form Edit Karyawan</h6>
        </div>
        <div class="card-body">
            <form method="post" action="" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="nik" class="form-label">NIK</label>
                            <input type="text" class="form-control" id="nik" name="nik" value="<?php echo $karyawan['nik']; ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="nama" class="form-label">Nama</label>
                            <input type="text" class="form-control" id="nama" name="nama" value="<?php echo $karyawan['nama']; ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="bidang_id" class="form-label">Bidang</label>
                            <select class="form-select" id="bidang_id" name="bidang_id" required>
                                <option value="">Pilih Bidang</option>
                                <?php foreach ($bidang_list as $b): ?>
                                    <option value="<?php echo $b['id']; ?>" <?php echo ($karyawan['bidang_id'] == $b['id']) ? 'selected' : ''; ?>>
                                        <?php echo $b['nama_bidang']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="jabatan" class="form-label">Jabatan</label>
                            <input type="text" class="form-control" id="jabatan" name="jabatan" value="<?php echo $karyawan['jabatan']; ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="keterangan" class="form-label">Keterangan</label>
                            <textarea class="form-control" id="keterangan" name="keterangan" rows="3"><?php echo $karyawan['keterangan']; ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="lokasi_id" class="form-label">Lokasi</label>
                            <select class="form-select" id="lokasi_id" name="lokasi_id" required>
                                <option value="">Pilih Lokasi</option>
                                <?php foreach ($lokasi as $l): ?>
                                    <option value="<?php echo $l['id']; ?>" <?php echo ($karyawan['lokasi_id'] == $l['id']) ? 'selected' : ''; ?>>
                                        <?php echo $l['nama_lokasi']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="foto_profil" class="form-label">Foto Profil</label>
                            <input type="file" class="form-control" id="foto_profil" name="foto_profil">
                            <small class="text-muted">Biarkan kosong jika tidak ingin mengubah foto profil</small>

                            <?php if (!empty($karyawan['foto_profil'])): ?>
                                <div class="mt-2">
                                    <img src="<?php echo BASE_URL . 'uploads/' . $karyawan['foto_profil']; ?>" alt="Foto Profil" class="img-thumbnail" style="max-width: 150px;">
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="allow_barcode" name="allow_barcode" <?php echo (isset($karyawan['allow_barcode']) && $karyawan['allow_barcode'] == 1) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="allow_barcode">
                                    <i class="fas fa-qrcode me-1"></i> Izinkan Absensi Barcode
                                </label>
                            </div>
                            <small class="text-muted">Centang untuk mengizinkan karyawan melakukan absensi menggunakan barcode</small>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <button type="submit" name="edit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan Perubahan
                    </button>
                    <a href="karyawan.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Batal
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
