<?php
// ====== DEBUG MODE ======
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// ====== KONEKSI & CONFIG ======
require_once '../config/database.php';
require_once '../config/config.php';

// ====== HEADER & CORS ======
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// ====== VALIDASI API KEY ======
function validateApiKey($api_key) {
    return $api_key === 'absensiku_api_key_2023';
}

// ====== AMBIL METHOD & INPUT ======
$method = $_SERVER['REQUEST_METHOD'];
$input = file_get_contents('php://input');
$data = json_decode($input, true);
if (!$data) $data = $_POST;

// API key dari GET jika tidak ada di body
if ($method === 'GET') {
    if (!isset($data['api_key']) && isset($_GET['api_key'])) {
        $data['api_key'] = $_GET['api_key'];
    }
}

// ====== VALIDASI API KEY ======
if (!isset($data['api_key']) || !validateApiKey($data['api_key'])) {
    echo json_encode(['status' => 'error', 'message' => 'API key tidak valid']);
    exit;
}

// ====== GET: Ambil data blokir ======
if ($method === 'GET') {
    $where = [];
    if (!empty($_GET['nik'])) {
        $nik = mysqli_real_escape_string($conn, $_GET['nik']);
        $where[] = "nik = '$nik'";
    }
    if (!empty($_GET['device_id'])) {
        $device_id = mysqli_real_escape_string($conn, $_GET['device_id']);
        $where[] = "device_id = '$device_id'";
    }

    $sql = "SELECT * FROM blokir_device";
    if (!empty($where)) {
        $sql .= " WHERE " . implode(" AND ", $where);
    }
    $sql .= " ORDER BY created_at DESC";

    $result = mysqli_query($conn, $sql);
    $rows = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $rows[] = $row;
    }

    echo json_encode(['status' => 'success', 'data' => $rows]);
    exit;
}

// ====== POST: Tambah blokir ======
if ($method === 'POST') {
    $fields = ['user_id', 'nik', 'device_id', 'alasan'];
    $values = [];

    foreach ($fields as $f) {
        $val = mysqli_real_escape_string($conn, $data[$f] ?? '');
        if (empty($val)) {
            echo json_encode(['status' => 'error', 'message' => "Field '$f' wajib diisi"]);
            exit;
        }
        $values[$f] = $val;
    }

    $status = 'blokir';
    $now = date('Y-m-d H:i:s');

    $sql = "INSERT INTO blokir_device (user_id, nik, device_id, alasan, status, created_at, updated_at)
            VALUES ('{$values['user_id']}', '{$values['nik']}', '{$values['device_id']}', '{$values['alasan']}', '$status', '$now', '$now')";

    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Device berhasil diblokir']);
    } else {
        echo json_encode(['status' => 'error', 'message' => mysqli_error($conn)]);
    }
    exit;
}

// ====== METHOD TIDAK DIDUKUNG ======
echo json_encode(['status' => 'error', 'message' => 'Method tidak diizinkan']);
exit;
