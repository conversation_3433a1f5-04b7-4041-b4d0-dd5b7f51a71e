<?php
// Include file konfigurasi
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/functions.php';

// Cek akses
checkAccess('admin');

// Cek apakah kolom keterangan sudah ada
$query = "SHOW COLUMNS FROM users LIKE 'keterangan'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    // Tambahkan kolom keterangan ke tabel users
    $query = "ALTER TABLE users ADD COLUMN keterangan TEXT DEFAULT NULL AFTER jabatan";
    
    if (mysqli_query($conn, $query)) {
        setMessage('success', 'Kolom keterangan berhasil ditambahkan ke tabel users!');
    } else {
        setMessage('danger', 'Gagal menambahkan kolom keterangan: ' . mysqli_error($conn));
    }
} else {
    setMessage('info', 'Kolom keterangan sudah ada di tabel users!');
}

// Redirect ke halaman karyawan
redirect('admin/karyawan.php');
?>
