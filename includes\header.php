<?php
// Include file konfigurasi
require_once dirname(dirname(__FILE__)) . '/config/config.php';

// Perbarui session lifetime jika user sudah login
if (isLoggedIn()) {
    // Perpanjang session lifetime
    $session_lifetime = 60 * 60 * 24 * 365 * 10; // 10 tahun dalam detik
    setcookie(session_name(), session_id(), time() + $session_lifetime, '/', $_SERVER['HTTP_HOST'], false, true);

    // Simpan device_id di cookie untuk identifikasi perangkat
    $device_id = $_SESSION['device_id'] ?? ($_COOKIE['device_id'] ?? null);
    if ($device_id) {
        setcookie('device_id', $device_id, time() + $session_lifetime, '/', $_SERVER['HTTP_HOST'], false, true);
    }
}

// Cek login
if (!isLoggedIn() && basename($_SERVER['PHP_SELF']) != 'index.php') {
    redirect('index.php');
}

// Tentukan apakah sidebar harus ditampilkan
$show_sidebar = isLoggedIn();

// Jangan tampilkan sidebar di halaman login utama
$script_name = $_SERVER['SCRIPT_NAME'] ?? $_SERVER['PHP_SELF'];
if (basename($script_name) == 'index.php' && strpos($script_name, '/admin/') === false && strpos($script_name, '/karyawan/') === false) {
    $show_sidebar = false;
}

// Jangan tampilkan sidebar jika variabel $no_sidebar diset
if (isset($no_sidebar) && $no_sidebar === true) {
    $show_sidebar = false;
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.1/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <!-- Leaflet Marker Cluster -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet.markercluster/1.5.3/MarkerCluster.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet.markercluster/1.5.3/MarkerCluster.Default.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet.markercluster/1.5.3/leaflet.markercluster.js"></script>

    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <!-- SweetAlert2 Helper -->
    <script src="<?php echo BASE_URL; ?>assets/js/sweetalert-helper.js"></script>

    <!-- Chart.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <!-- Fallback Chart.js -->
    <script>
        if (typeof Chart === 'undefined') {
            document.write('<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"><\/script>');
        }
    </script>

    <!-- CSS Styles based on role -->
    <?php
    $is_mobile_app = isset($_SESSION['role']) && $_SESSION['role'] == 'karyawan' && basename($_SERVER['PHP_SELF']) == 'index.php';
    $is_mobile_presensi = isset($_SESSION['role']) && $_SESSION['role'] == 'karyawan' && basename($_SERVER['PHP_SELF']) == 'presensi.php';
    $is_mobile_riwayat = isset($_SESSION['role']) && $_SESSION['role'] == 'karyawan' && basename($_SERVER['PHP_SELF']) == 'riwayat.php';
    $is_mobile_izin_dinas = isset($_SESSION['role']) && $_SESSION['role'] == 'karyawan' && basename($_SERVER['PHP_SELF']) == 'izin_dinas.php';
    $is_mobile_profile = isset($_SESSION['role']) && $_SESSION['role'] == 'karyawan' && basename($_SERVER['PHP_SELF']) == 'profile.php';
    $is_mobile_gangguan_absensi = isset($_SESSION['role']) && $_SESSION['role'] == 'karyawan' && basename($_SERVER['PHP_SELF']) == 'gangguan_absensi.php';
    $is_mobile_scan_barcode = isset($_SESSION['role']) && $_SESSION['role'] == 'karyawan' && basename($_SERVER['PHP_SELF']) == 'scan_barcode.php';
    $is_mobile_scan_rapat = isset($_SESSION['role']) && $_SESSION['role'] == 'karyawan' && basename($_SERVER['PHP_SELF']) == 'scan_rapat.php';

    // Cek apakah halaman karyawan (untuk bottom nav)
    $is_karyawan_page = isset($_SESSION['role']) && $_SESSION['role'] == 'karyawan' &&
                        (strpos($_SERVER['PHP_SELF'], '/karyawan/') !== false ||
                         basename($_SERVER['PHP_SELF']) == 'profile.php');

    // Cek apakah halaman admin
    $is_admin_page = isset($_SESSION['role']) && $_SESSION['role'] == 'admin' &&
                    (strpos($_SERVER['PHP_SELF'], '/admin/') !== false);

    // Load CSS untuk halaman admin
    if ($is_admin_page): ?>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="<?php echo BASE_URL; ?>assets/css/admin-style.css" rel="stylesheet">
    <?php endif; ?>

    <!-- Mobile App CSS (for karyawan dashboard) -->
    <?php if ($is_mobile_app): ?>
    <link href="<?php echo BASE_URL; ?>assets/css/mobile-app.css" rel="stylesheet">
    <?php endif; ?>

    <?php if ($is_mobile_presensi): ?>
    <link href="<?php echo BASE_URL; ?>assets/css/mobile-presensi.css" rel="stylesheet">
    <?php endif; ?>

    <?php if ($is_mobile_riwayat): ?>
    <link href="<?php echo BASE_URL; ?>assets/css/mobile-riwayat.css" rel="stylesheet">
    <?php endif; ?>

    <?php if ($is_mobile_izin_dinas): ?>
    <link href="<?php echo BASE_URL; ?>assets/css/mobile-izin-dinas.css" rel="stylesheet">
    <?php endif; ?>

    <?php if ($is_mobile_profile): ?>
    <link href="<?php echo BASE_URL; ?>assets/css/mobile-profile.css" rel="stylesheet">
    <?php endif; ?>

    <?php if ($is_mobile_gangguan_absensi): ?>
    <link href="<?php echo BASE_URL; ?>assets/css/mobile-gangguan-absensi.css" rel="stylesheet">
    <?php endif; ?>

    <?php if ($is_mobile_scan_barcode): ?>
    <link href="<?php echo BASE_URL; ?>assets/css/mobile-scan-barcode.css" rel="stylesheet">
    <?php endif; ?>

    <?php if ($is_mobile_scan_rapat): ?>
    <link href="<?php echo BASE_URL; ?>assets/css/mobile-presensi.css" rel="stylesheet">
    <?php endif; ?>

    <?php if ($is_karyawan_page): ?>
    <link href="<?php echo BASE_URL; ?>assets/css/bottom-nav.css" rel="stylesheet">
    <?php endif; ?>

    <!-- Custom CSS for non-admin, non-mobile pages -->
    <?php if (!$is_admin_page && !$is_karyawan_page): ?>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: bold;
        }
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }
        .btn-primary:hover {
            background-color: #0069d9;
            border-color: #0062cc;
        }
    </style>
    <?php endif; ?>
</head>
<body>
    <div class="container-fluid">
        <?php if ($show_sidebar): ?>
            <?php if ((!isset($is_mobile_app) || !$is_mobile_app) &&
                  (!isset($is_mobile_presensi) || !$is_mobile_presensi) &&
                  (!isset($is_mobile_riwayat) || !$is_mobile_riwayat) &&
                  (!isset($is_mobile_profile) || !$is_mobile_profile) &&
                  (!isset($is_mobile_izin_dinas) || !$is_mobile_izin_dinas) &&
                  (!isset($is_mobile_gangguan_absensi) || !$is_mobile_gangguan_absensi) &&
                  (!isset($is_mobile_scan_barcode) || !$is_mobile_scan_barcode) &&
                  (!isset($is_mobile_scan_rapat) || !$is_mobile_scan_rapat)): ?>
            <!-- Overlay for sidebar on mobile -->
            <div class="overlay"></div>

            <!-- Sidebar -->
            <div class="row">
                <div class="col-md-2 col-lg-2 d-md-block sidebar">
                    <?php include_once 'sidebar.php'; ?>
                </div>
            </div>

            <!-- Content -->
            <div class="content">
                <!-- Navbar -->
                <nav class="navbar navbar-expand-lg navbar-light mb-4">
                    <div class="container-fluid">
                        <button class="btn btn-dark d-md-none me-3" id="sidebarToggle">
                            <i class="fas fa-bars"></i>
                        </button>
                        <a class="navbar-brand d-md-none" href="#"><?php echo APP_NAME; ?></a>
                        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                        <div class="collapse navbar-collapse" id="navbarNav">
                            <ul class="navbar-nav ms-auto">
                                <li class="nav-item me-3">
                                    <a class="nav-link" href="<?php echo BASE_URL; ?>help.php">
                                        <i class="fas fa-question-circle me-1"></i> Bantuan
                                    </a>
                                </li>
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-user-circle me-1"></i> <?php echo $_SESSION['nama']; ?>
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <?php if ($_SESSION['role'] == 'admin'): ?>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/profile.php"><i class="fas fa-user me-1"></i> Profil Admin</a></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/change_password.php"><i class="fas fa-key me-1"></i> Ubah Password</a></li>
                                        <?php else: ?>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/profile.php"><i class="fas fa-user me-1"></i> Profil</a></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>admin/change_password.php"><i class="fas fa-key me-1"></i> Ubah Password</a></li>
                                        <?php endif; ?>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>logout.php"><i class="fas fa-sign-out-alt me-1"></i> Logout</a></li>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </div>
                </nav>
            <?php else: ?>
                <!-- No sidebar or navbar for mobile app view -->
            <?php endif; ?>

                <?php
                // Tampilkan pesan jika ada
                $message = getMessage();
                if ($message): ?>
                    <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show">
                        <?php echo $message['text']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

        <?php else: ?>
            <!-- Login Page -->
            <div class="col-12">
        <?php endif; ?>
