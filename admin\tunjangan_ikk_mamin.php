<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Cek dan buat tabel pengaturan_tunjangan jika belum ada
$query = "SHOW TABLES LIKE 'pengaturan_tunjangan'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    // Buat tabel pengaturan_tunjangan
    $query = "CREATE TABLE pengaturan_tunjangan (
        id INT AUTO_INCREMENT PRIMARY KEY,
        bulan VARCHAR(2) NOT NULL,
        tahun VARCHAR(4) NOT NULL,
        ikk_bulanan DECIMAL(10,2) NOT NULL DEFAULT 0,
        mamin_bulanan DECIMAL(10,2) NOT NULL DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_bulan_tahun (bulan, tahun)
    )";
    
    if (!mysqli_query($conn, $query)) {
        die("Error creating table: " . mysqli_error($conn));
    }
}

// Proses form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $bulan = clean($_POST['bulan']);
    $tahun = clean($_POST['tahun']);
    $ikk_bulanan = clean($_POST['ikk_bulanan']);
    $mamin_bulanan = clean($_POST['mamin_bulanan']);
    
    // Simpan pengaturan IKK dan MAMIN
    $sql = "INSERT INTO pengaturan_tunjangan (bulan, tahun, ikk_bulanan, mamin_bulanan) 
            VALUES (?, ?, ?, ?) 
            ON DUPLICATE KEY UPDATE 
            ikk_bulanan = VALUES(ikk_bulanan), 
            mamin_bulanan = VALUES(mamin_bulanan)";
    
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        die("Error preparing statement: " . $conn->error);
    }
    
    $stmt->bind_param("ssdd", $bulan, $tahun, $ikk_bulanan, $mamin_bulanan);
    
    if ($stmt->execute()) {
        setMessage('success', 'Pengaturan tunjangan berhasil disimpan!');
    } else {
        setMessage('danger', 'Gagal menyimpan pengaturan tunjangan: ' . $conn->error);
    }
    
    redirect('admin/tunjangan_ikk_mamin.php');
}

// Ambil data pengaturan terbaru
$sql = "SELECT * FROM pengaturan_tunjangan ORDER BY tahun DESC, bulan DESC LIMIT 1";
$result = $conn->query($sql);
$pengaturan = $result->fetch_assoc();

// Set nilai default jika belum ada pengaturan
$bulan = $pengaturan ? $pengaturan['bulan'] : date('m');
$tahun = $pengaturan ? $pengaturan['tahun'] : date('Y');
$ikk_bulanan = $pengaturan ? $pengaturan['ikk_bulanan'] : 0;
$mamin_bulanan = $pengaturan ? $pengaturan['mamin_bulanan'] : 0;

// Hitung total hari kerja
$sql_hari_kerja = "SELECT COUNT(DISTINCT tanggal) as total_hari_kerja 
                   FROM presensi 
                   WHERE MONTH(tanggal) = ? AND YEAR(tanggal) = ?";
$stmt_hari = $conn->prepare($sql_hari_kerja);
if ($stmt_hari === false) {
    die("Error preparing statement: " . $conn->error);
}

$stmt_hari->bind_param("ss", $bulan, $tahun);
$stmt_hari->execute();
$result_hari = $stmt_hari->get_result();
$total_hari_kerja = $result_hari->fetch_assoc()['total_hari_kerja'];

// Ambil data karyawan dengan perhitungan tunjangan (kecuali karyawan koperasi)
$sql = "SELECT
            u.nik,
            u.nama,
            COUNT(CASE WHEN p.status = 'Tepat Waktu' THEN 1 END) as tepat_waktu,
            COUNT(CASE WHEN p.status = 'Terlambat' THEN 1 END) as terlambat,
            COUNT(CASE WHEN p.status = 'Pulang Awal' THEN 1 END) as pulang_awal,
            COUNT(CASE WHEN p.status = 'Lembur' THEN 1 END) as lembur,
            COUNT(p.id) as total_kehadiran
        FROM users u
        LEFT JOIN presensi p ON u.id = p.user_id
            AND MONTH(p.tanggal) = ?
            AND YEAR(p.tanggal) = ?
        WHERE u.role = 'karyawan' AND (u.keterangan != 'koperasi' OR u.keterangan IS NULL)
        GROUP BY u.id, u.nik, u.nama";

$stmt = $conn->prepare($sql);
if ($stmt === false) {
    die("Error preparing statement: " . $conn->error);
}

$stmt->bind_param("ss", $bulan, $tahun);
$stmt->execute();
$result = $stmt->get_result();

// Ambil pengaturan denda
$sql_denda = "SELECT * FROM denda ORDER BY id DESC LIMIT 1";
$result_denda = $conn->query($sql_denda);
$denda = $result_denda->fetch_assoc();

// Include header
include_once '../includes/header.php';

// Ambil pesan jika ada
$message = getMessage();
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Tunjangan IKK & MAMIN</h1>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show" role="alert">
            <?php echo $message['text']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Form Input -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Pengaturan Tunjangan Bulanan</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="">
                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="bulan" class="form-label">Bulan</label>
                            <select class="form-select" id="bulan" name="bulan" required>
                                <?php
                                $bulan_list = [
                                    '01' => 'Januari', '02' => 'Februari', '03' => 'Maret',
                                    '04' => 'April', '05' => 'Mei', '06' => 'Juni',
                                    '07' => 'Juli', '08' => 'Agustus', '09' => 'September',
                                    '10' => 'Oktober', '11' => 'November', '12' => 'Desember'
                                ];
                                foreach ($bulan_list as $key => $value) {
                                    $selected = ($key == $bulan) ? 'selected' : '';
                                    echo "<option value='$key' $selected>$value</option>";
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="tahun" class="form-label">Tahun</label>
                            <select class="form-select" id="tahun" name="tahun" required>
                                <?php
                                $tahun_sekarang = date('Y');
                                for ($i = $tahun_sekarang; $i >= $tahun_sekarang - 2; $i--) {
                                    $selected = ($i == $tahun) ? 'selected' : '';
                                    echo "<option value='$i' $selected>$i</option>";
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="ikk_bulanan" class="form-label">IKK Bulanan</label>
                            <input type="number" class="form-control" id="ikk_bulanan" name="ikk_bulanan" 
                                   value="<?php echo $ikk_bulanan; ?>" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="mamin_bulanan" class="form-label">MAMIN Bulanan</label>
                            <input type="number" class="form-control" id="mamin_bulanan" name="mamin_bulanan" 
                                   value="<?php echo $mamin_bulanan; ?>" required>
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Simpan Pengaturan
                </button>
            </form>
        </div>
    </div>

    <!-- Tabel Data -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Data Tunjangan Karyawan</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>NIK</th>
                            <th>Nama</th>
                            <th>Kehadiran</th>
                            <th>Total Denda</th>
                            <th>Total IKK</th>
                            <th>Total MAMIN</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($row = $result->fetch_assoc()): 
                            // Hitung denda berdasarkan keterlambatan dan pulang awal
                            $total_denda = ($row['terlambat'] * $denda['denda_masuk']) + 
                                         ($row['pulang_awal'] * $denda['denda_pulang']);
                            
                            // Hitung tunjangan berdasarkan proporsi kehadiran
                            $total_ikk = ($ikk_bulanan / $total_hari_kerja) * $row['total_kehadiran'];
                            $total_mamin = ($mamin_bulanan / $total_hari_kerja) * $row['total_kehadiran'];
                        ?>
                        <tr>
                            <td><?php echo $row['nik']; ?></td>
                            <td><?php echo $row['nama']; ?></td>
                            <td>
                                Tepat Waktu: <?php echo $row['tepat_waktu']; ?><br>
                                Terlambat: <?php echo $row['terlambat']; ?><br>
                                Pulang Awal: <?php echo $row['pulang_awal']; ?><br>
                                Lembur: <?php echo $row['lembur']; ?>
                            </td>
                            <td>Rp. <?php echo number_format($total_denda, 0, ',', '.'); ?></td>
                            <td>Rp. <?php echo number_format($total_ikk, 0, ',', '.'); ?></td>
                            <td>Rp. <?php echo number_format($total_mamin, 0, ',', '.'); ?></td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#dataTable').DataTable({
        responsive: true,
        order: [[0, 'asc']]
    });
});
</script>

<?php
// Include footer
include_once '../includes/footer.php';
?>
