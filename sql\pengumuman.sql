-- Buat tabel pengumuman
CREATE TABLE IF NOT EXISTS `pengumuman` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `judul` varchar(255) NOT NULL,
  `tanggal` date NOT NULL,
  `tanggal_kadaluarsa` date NOT NULL,
  `foto` varchar(255) DEFAULT NULL,
  `status` enum('aktif','kadaluarsa') NOT NULL DEFAULT 'aktif',
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Trigger untuk update status otomatis
DELIMITER //
CREATE TRIGGER update_status_pengumuman
BEFORE INSERT ON pengumuman
FOR EACH ROW
BEGIN
    IF NEW.tanggal_kadaluarsa < CURDATE() THEN
        SET NEW.status = 'kadaluarsa';
    ELSE
        SET NEW.status = 'aktif';
    END IF;
END//

CREATE TRIGGER update_status_pengumuman_update
BEFORE UPDATE ON pengumuman
FOR EACH ROW
BEGIN
    IF NEW.tanggal_kadaluarsa < CURDATE() THEN
        SET NEW.status = 'kadaluarsa';
    ELSE
        SET NEW.status = 'aktif';
    END IF;
END//
DELIMITER ; 