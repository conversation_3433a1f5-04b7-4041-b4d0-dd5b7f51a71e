<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Filter tanggal, pencarian, dan bidang
$tanggal = isset($_GET['tanggal']) ? $_GET['tanggal'] : date('Y-m-d');
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$filter_bidang = isset($_GET['bidang']) ? trim($_GET['bidang']) : '';

// Ambil data bidang untuk dropdown filter
$query_bidang = "SELECT DISTINCT b.id, b.nama_bidang
                 FROM bidang b
                 JOIN users u ON b.id = u.bidang_id
                 WHERE u.role = 'karyawan'
                 ORDER BY b.nama_bidang ASC";
$result_bidang = mysqli_query($conn, $query_bidang);
$bidang_list = [];
if ($result_bidang) {
    while ($row = mysqli_fetch_assoc($result_bidang)) {
        $bidang_list[] = $row;
    }
}

// Query untuk data presensi dengan pencarian dan filter bidang
$query = "SELECT p.*, u.nik, u.nama, u.bidang_id, b.nama_bidang as bidang, u.jabatan
          FROM presensi p
          JOIN users u ON p.user_id = u.id
          LEFT JOIN bidang b ON u.bidang_id = b.id
          WHERE p.tanggal = '$tanggal'";

// Tambahkan kondisi pencarian jika ada
if (!empty($search)) {
    $search_escaped = mysqli_real_escape_string($conn, $search);
    $query .= " AND (u.nama LIKE '%$search_escaped%' OR u.nik LIKE '%$search_escaped%' OR b.nama_bidang LIKE '%$search_escaped%')";
}

// Tambahkan filter bidang jika ada
if (!empty($filter_bidang)) {
    $filter_bidang_escaped = mysqli_real_escape_string($conn, $filter_bidang);
    $query .= " AND u.bidang_id = '$filter_bidang_escaped'";
}

$query .= " ORDER BY p.jam_masuk ASC";
$result = mysqli_query($conn, $query);

$presensi = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $presensi[] = $row;
    }
}

// Include header
include_once '../includes/header.php';
?>

<style>
.photo-thumbnail {
    position: relative;
    display: inline-block;
}

.photo-preview {
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.photo-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.photo-thumbnail::after {
    content: '\f065'; /* Font Awesome expand icon */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(0,0,0,0.7);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.photo-thumbnail:hover::after {
    opacity: 1;
}

.table td {
    vertical-align: middle;
}
</style>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Monitoring Presensi</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="m-0 font-weight-bold">Data Presensi</h6>
                <div class="d-flex gap-2">
                    <a href="absensi_manual.php?tanggal=<?php echo $tanggal; ?>" class="btn btn-success">
                        <i class="fas fa-user-clock"></i> Absensi Manual
                    </a>
                    <button type="button" class="btn btn-warning" id="btnScanFoto" disabled>
                        <i class="fas fa-spinner fa-spin"></i> Loading Face API...
                    </button>
                </div>
            </div>

            <!-- Filter dan Pencarian -->
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label small">Filter Tanggal</label>
                    <form method="get" class="d-flex">
                        <input type="date" class="form-control me-2" name="tanggal" value="<?php echo $tanggal; ?>">
                        <?php if (!empty($search)): ?>
                            <input type="hidden" name="search" value="<?php echo htmlspecialchars($search); ?>">
                        <?php endif; ?>
                        <?php if (!empty($filter_bidang)): ?>
                            <input type="hidden" name="bidang" value="<?php echo htmlspecialchars($filter_bidang); ?>">
                        <?php endif; ?>
                        <button type="submit" class="btn btn-primary btn-sm">Filter</button>
                    </form>
                </div>
                <div class="col-md-3">
                    <label class="form-label small">Filter Bidang</label>
                    <form method="get" class="d-flex">
                        <input type="hidden" name="tanggal" value="<?php echo $tanggal; ?>">
                        <?php if (!empty($search)): ?>
                            <input type="hidden" name="search" value="<?php echo htmlspecialchars($search); ?>">
                        <?php endif; ?>
                        <select class="form-control me-2" name="bidang" onchange="this.form.submit()">
                            <option value="">Semua Bidang</option>
                            <?php foreach ($bidang_list as $bidang): ?>
                                <option value="<?php echo $bidang['id']; ?>" <?php echo $filter_bidang == $bidang['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($bidang['nama_bidang']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </form>
                </div>
                <div class="col-md-4">
                    <label class="form-label small">Pencarian</label>
                    <form method="get" class="d-flex">
                        <input type="hidden" name="tanggal" value="<?php echo $tanggal; ?>">
                        <?php if (!empty($filter_bidang)): ?>
                            <input type="hidden" name="bidang" value="<?php echo htmlspecialchars($filter_bidang); ?>">
                        <?php endif; ?>
                        <input type="text" class="form-control me-2" name="search" placeholder="Cari nama, NIK..." value="<?php echo htmlspecialchars($search); ?>">
                        <button type="submit" class="btn btn-info btn-sm">Cari</button>
                    </form>
                </div>
                <div class="col-md-2">
                    <label class="form-label small">&nbsp;</label>
                    <div class="d-flex">
                        <?php if (!empty($search) || !empty($filter_bidang)): ?>
                            <a href="monitoring.php?tanggal=<?php echo $tanggal; ?>" class="btn btn-secondary btn-sm">
                                <i class="fas fa-times"></i> Reset Filter
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

            </div>

            <!-- Informasi Filter Aktif -->
            <?php if (!empty($search) || !empty($filter_bidang)): ?>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-filter"></i> Filter aktif:
                        <?php if (!empty($search)): ?>
                            <span class="badge bg-info">Pencarian: "<?php echo htmlspecialchars($search); ?>"</span>
                        <?php endif; ?>
                        <?php if (!empty($filter_bidang)): ?>
                            <?php
                            $nama_bidang_filter = '';
                            foreach ($bidang_list as $bidang) {
                                if ($bidang['id'] == $filter_bidang) {
                                    $nama_bidang_filter = $bidang['nama_bidang'];
                                    break;
                                }
                            }
                            ?>
                            <span class="badge bg-success">Bidang: <?php echo htmlspecialchars($nama_bidang_filter); ?></span>
                        <?php endif; ?>
                    </small>
                </div>
            <?php endif; ?>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>NIK</th>
                            <th>Nama</th>
                            <th>Bidang</th>
                            <th>Jam Masuk</th>
                            <th>Foto Masuk</th>
                            <th>Lokasi Masuk</th>
                            <th>Jam Pulang</th>
                            <th>Foto Pulang</th>
                            <th>Lokasi Pulang</th>
                            <th>Status</th>
                            <th>Keterangan</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($presensi)): ?>
                            <tr>
                                <td colspan="12" class="text-center">
                                    <?php if (!empty($search)): ?>
                                        <div class="py-4">
                                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">Tidak ada data yang ditemukan</h5>
                                            <p class="text-muted">
                                                Tidak ada data presensi yang cocok dengan pencarian "<strong><?php echo htmlspecialchars($search); ?></strong>"
                                                pada tanggal <?php echo date('d/m/Y', strtotime($tanggal)); ?>
                                            </p>
                                            <a href="?tanggal=<?php echo $tanggal; ?>" class="btn btn-outline-primary">
                                                <i class="fas fa-times"></i> Reset Pencarian
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <div class="py-4">
                                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">Tidak ada data presensi</h5>
                                            <p class="text-muted">
                                                Belum ada data presensi pada tanggal <?php echo date('d/m/Y', strtotime($tanggal)); ?>
                                            </p>
                                        </div>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($presensi as $p): ?>
                                <tr>
                                    <td><?php echo $p['nik']; ?></td>
                                    <td><?php echo $p['nama']; ?></td>
                                    <td><?php echo $p['bidang'] ?? '-'; ?></td>
                                    <td><?php echo $p['jam_masuk']; ?></td>
                                    <td>
                                        <?php if (!empty($p['foto_masuk'])): ?>
                                            <div class="photo-thumbnail">
                                                <img src="<?php echo BASE_URL . 'uploads/' . $p['foto_masuk']; ?>"
                                                     alt="Foto Masuk"
                                                     class="img-thumbnail photo-preview"
                                                     style="width: 60px; height: 60px; object-fit: cover; cursor: pointer;"
                                                     onclick="showPhotoModal('<?php echo BASE_URL . 'uploads/' . $p['foto_masuk']; ?>', 'Foto Masuk - <?php echo $p['nama']; ?>')">
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $p['lokasi_masuk'] ?? '-'; ?></td>
                                    <td><?php echo $p['jam_pulang'] ?? '-'; ?></td>
                                    <td>
                                        <?php if (!empty($p['foto_pulang'])): ?>
                                            <div class="photo-thumbnail">
                                                <img src="<?php echo BASE_URL . 'uploads/' . $p['foto_pulang']; ?>"
                                                     alt="Foto Pulang"
                                                     class="img-thumbnail photo-preview"
                                                     style="width: 60px; height: 60px; object-fit: cover; cursor: pointer;"
                                                     onclick="showPhotoModal('<?php echo BASE_URL . 'uploads/' . $p['foto_pulang']; ?>', 'Foto Pulang - <?php echo $p['nama']; ?>')">
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($p['lokasi_pulang'] == 'Perjalanan Dinas'): ?>
                                            <span class="badge bg-primary">Perjalanan Dinas</span>
                                        <?php else: ?>
                                            <?php echo $p['lokasi_pulang'] ?? '-'; ?>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($p['status'] == 'Tepat Waktu'): ?>
                                            <span class="badge bg-success">Tepat Waktu</span>
                                        <?php elseif ($p['status'] == 'Terlambat'): ?>
                                            <span class="badge bg-warning">Terlambat</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary"><?php echo $p['status']; ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (strpos($p['keterangan'], 'Absensi manual') !== false): ?>
                                            <span class="badge bg-info">Manual</span>
                                        <?php endif; ?>
                                        <?php if (strpos($p['keterangan'], 'Perjalanan Dinas') !== false): ?>
                                            <span class="badge bg-primary">Perjalanan Dinas</span>
                                        <?php endif; ?>
                                        <?php echo $p['keterangan'] ?? '-'; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="detail_presensi.php?id=<?php echo $p['id']; ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> Detail
                                            </a>
                                            <a href="edit_presensi.php?id=<?php echo $p['id']; ?>" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <?php if (empty($p['jam_pulang']) && strpos($p['keterangan'], 'Perjalanan Dinas') === false): ?>
                                            <a href="tandai_dinas.php?id=<?php echo $p['id']; ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-plane"></i> Tandai Dinas
                                            </a>
                                            <?php endif; ?>
                                            <button type="button" class="btn btn-sm btn-danger btn-delete" data-id="<?php echo $p['id']; ?>">
                                                <i class="fas fa-trash"></i> Hapus
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal untuk menampilkan foto -->
<div class="modal fade" id="photoModal" tabindex="-1" aria-labelledby="photoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="photoModalLabel">Foto Presensi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalPhoto" src="" alt="Foto Presensi" class="img-fluid" style="max-height: 70vh;">
            </div>
            <div class="modal-footer">
                <a id="downloadPhoto" href="" download class="btn btn-success">
                    <i class="fas fa-download"></i> Download
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Scan Foto -->
<div class="modal fade" id="modalScanFoto" tabindex="-1" aria-labelledby="modalScanFotoLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalScanFotoLabel">Scan Foto Presensi - Deteksi Wajah</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Kontrol Periode -->
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="scanBulan" class="form-label">Bulan:</label>
                        <select class="form-select" id="scanBulan">
                            <option value="1">Januari</option>
                            <option value="2">Februari</option>
                            <option value="3">Maret</option>
                            <option value="4">April</option>
                            <option value="5">Mei</option>
                            <option value="6">Juni</option>
                            <option value="7">Juli</option>
                            <option value="8">Agustus</option>
                            <option value="9">September</option>
                            <option value="10">Oktober</option>
                            <option value="11">November</option>
                            <option value="12">Desember</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="scanTahun" class="form-label">Tahun:</label>
                        <select class="form-select" id="scanTahun">
                            <?php for ($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                                <option value="<?php echo $y; ?>"><?php echo $y; ?></option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="button" class="btn btn-primary" id="btnMulaiScan">
                            <i class="fas fa-play"></i> Mulai Scan
                        </button>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="progress mb-3" id="progressScan" style="display: none;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                </div>

                <!-- Status Scan -->
                <div id="statusScan" class="alert alert-info" style="display: none;"></div>

                <!-- Hasil Scan -->
                <div id="hasilScan" style="display: none;">
                    <h6>Foto yang Tidak Terdeteksi Wajah:</h6>
                    <div id="daftarFotoNoFace"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>

<script>
// Fungsi untuk menampilkan foto dalam modal
function showPhotoModal(photoUrl, title) {
    const modal = new bootstrap.Modal(document.getElementById('photoModal'));
    const modalPhoto = document.getElementById('modalPhoto');
    const modalTitle = document.getElementById('photoModalLabel');
    const downloadLink = document.getElementById('downloadPhoto');

    // Set foto dan title
    modalPhoto.src = photoUrl;
    modalTitle.textContent = title;
    downloadLink.href = photoUrl;

    // Tampilkan modal
    modal.show();
}

document.addEventListener('DOMContentLoaded', function() {
    // Konfirmasi hapus presensi
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');

            Swal.fire({
                title: 'Konfirmasi Hapus',
                text: 'Apakah Anda yakin ingin menghapus data presensi ini?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Ya, Hapus!',
                cancelButtonText: 'Batal',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = 'hapus_presensi.php?id=' + id;
                }
            });
        });
    });
});
</script>

<!-- Load face-api.js (local-first with multi-CDN fallback) -->
<script>
// Konfigurasi path lokal dan CDN
const FACE_API_LOCAL_BASE = '../assets/face-api';
const FACE_API_LOCAL_WEIGHTS = FACE_API_LOCAL_BASE + '/weights';

// Load face-api.js dynamically (prioritas lokal)
(function() {
    function attachOnLoad(el) {
        el.onload = function() {
            if (typeof window.onFaceApiLoaded === 'function') {
                window.onFaceApiLoaded();
            } else {
                window.__faceApiLibLoaded = true;
            }
        };
    }

    function loadFrom(url, onErrorNext) {
        const s = document.createElement('script');
        s.src = url;
        attachOnLoad(s);
        s.onerror = onErrorNext;
        document.head.appendChild(s);
    }

    // 1) Coba lokal
    loadFrom(
        FACE_API_LOCAL_BASE + '/face-api.min.js',
        function() {
            console.error('Failed to load face-api.js from local, trying CDN vladmandic...');
            // 2) Coba CDN @vladmandic
            loadFrom(
                'https://cdn.jsdelivr.net/npm/@vladmandic/face-api/dist/face-api.min.js',
                function() {
                    console.error('Failed to load @vladmandic/face-api, trying jsDelivr 0.22.2...');
                    // 3) Coba jsDelivr 0.22.2
                    loadFrom(
                        'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js',
                        function() {
                            console.error('Failed to load face-api.js from jsDelivr 0.22.2, trying unpkg 0.22.2...');
                            // 4) Coba unpkg 0.22.2
                            loadFrom(
                                'https://unpkg.com/face-api.js@0.22.2/dist/face-api.min.js',
                                function() {
                                    console.error('Failed to load face-api.js from all sources');
                                    const btnScanFoto = document.getElementById('btnScanFoto');
                                    if (btnScanFoto) {
                                        btnScanFoto.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Face API Load Failed';
                                        btnScanFoto.className = 'btn btn-danger';
                                        btnScanFoto.onclick = function() {
                                            alert('Library deteksi wajah gagal dimuat. Pastikan koneksi ke CDN atau letakkan file lokal di \'assets/face-api\'.');
                                        };
                                    }
                                }
                            );
                        }
                    );
                }
            );
        }
    );
})();
</script>

<script>
// Global variables untuk face-api.js
let faceApiModelsLoaded = false;
let isScanning = false;
let faceApiReady = false;

// Callback ketika face-api.js selesai dimuat
window.onFaceApiLoaded = function onFaceApiLoaded() {
    faceApiReady = true;
    console.log('Face API library loaded successfully');
    
    // Update UI untuk menunjukkan library sudah dimuat
    const btnScanFoto = document.getElementById('btnScanFoto');
    if (btnScanFoto) {
        btnScanFoto.innerHTML = '<i class="fas fa-search"></i> Scan Foto (Periode Bulan)';
        btnScanFoto.disabled = false;
    }
    
    // Load models jika DOM sudah ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadFaceApiModels);
    } else {
        loadFaceApiModels();
    }
};

// Fallback jika onload tidak berfungsi
setTimeout(function() {
    if (!faceApiReady && (typeof faceapi !== 'undefined' || window.__faceApiLibLoaded)) {
        console.log('Face API detected via fallback');
        if (typeof window.onFaceApiLoaded === 'function') {
            window.onFaceApiLoaded();
        }
    }
}, 2000);

// Additional fallback untuk memastikan script dimuat
document.addEventListener('DOMContentLoaded', function() {
    // Check lagi setelah DOM ready
    setTimeout(function() {
        if (!faceApiReady && (typeof faceapi !== 'undefined' || window.__faceApiLibLoaded)) {
            console.log('Face API detected after DOM ready');
            if (typeof window.onFaceApiLoaded === 'function') {
                window.onFaceApiLoaded();
            }
        }
    }, 1000);
});

// Load models face-api.js
async function loadFaceApiModels() {
    if (!faceApiReady) {
        console.log('Face API not ready yet, waiting...');
        setTimeout(loadFaceApiModels, 100);
        return;
    }
    
    try {
        console.log('Loading TinyFaceDetector model (local)...');
        await faceapi.nets.tinyFaceDetector.loadFromUri(FACE_API_LOCAL_WEIGHTS);
        console.log('Loading FaceLandmark68Net model (local)...');
        await faceapi.nets.faceLandmark68Net.loadFromUri(FACE_API_LOCAL_WEIGHTS);
        faceApiModelsLoaded = true;
        console.log('Face API models loaded successfully');
    } catch (error) {
        console.error('Error loading Face API models:', error);
        console.error('faceapi object:', typeof faceapi);
        console.error('faceapi.nets:', faceapi.nets);
        
        // Try alternative CDN for models
        try {
            console.log('Trying jsDelivr 0.22.2 for models...');
            await faceapi.nets.tinyFaceDetector.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights');
            await faceapi.nets.faceLandmark68Net.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights');
            faceApiModelsLoaded = true;
            console.log('Face API models loaded successfully from jsDelivr');
        } catch (error2) {
            try {
                console.log('Trying unpkg 0.22.2 for models...');
                await faceapi.nets.tinyFaceDetector.loadFromUri('https://unpkg.com/face-api.js@0.22.2/weights');
                await faceapi.nets.faceLandmark68Net.loadFromUri('https://unpkg.com/face-api.js@0.22.2/weights');
                faceApiModelsLoaded = true;
                console.log('Face API models loaded successfully from unpkg');
            } catch (error3) {
                console.error('Failed to load models from all sources:', error3);
                alert('Gagal memuat model deteksi wajah. Pastikan koneksi ke CDN atau letakkan file model lokal di folder assets/face-api/weights.');
            }
        }
    }
}

// Deteksi wajah dalam satu gambar
async function detectFaceInImage(imageUrl) {
    try {
        // Pastikan faceapi tersedia
        if (typeof faceapi === 'undefined') {
            throw new Error('Face API library not loaded');
        }
        
        const img = await faceapi.fetchImage(imageUrl);
        const detections = await faceapi.detectAllFaces(img, new faceapi.TinyFaceDetectorOptions());
        return {
            hasFace: detections.length > 0,
            faceCount: detections.length,
            error: null
        };
    } catch (error) {
        console.error('Error detecting face in image:', error);
        return {
            hasFace: false,
            faceCount: 0,
            error: error.message
        };
    }
}

// Mulai scan foto
async function mulaiScanFoto() {
    if (isScanning) return;
    
    const bulan = document.getElementById('scanBulan').value;
    const tahun = document.getElementById('scanTahun').value;
    
    if (!faceApiModelsLoaded) {
        alert('Model deteksi wajah belum siap. Silakan tunggu sebentar.');
        return;
    }
    
    if (typeof faceapi === 'undefined') {
        alert('Library deteksi wajah belum dimuat. Silakan refresh halaman.');
        return;
    }
    
    isScanning = true;
    document.getElementById('btnMulaiScan').disabled = true;
    document.getElementById('progressScan').style.display = 'block';
    document.getElementById('statusScan').style.display = 'block';
    document.getElementById('hasilScan').style.display = 'none';
    
    try {
        // Ambil data foto dari server
        const response = await fetch(`scan_foto.php?bulan=${bulan}&tahun=${tahun}`);
        const data = await response.json();
        
        if (data.status !== 'success') {
            throw new Error(data.message || 'Gagal mengambil data foto');
        }
        
        if (data.total_checked === 0) {
            document.getElementById('statusScan').innerHTML = '<i class="fas fa-info-circle"></i> Tidak ada foto pada periode ini.';
            return;
        }
        
        // Flatten semua file foto
        const allFiles = [];
        data.data.forEach(entry => {
            entry.files.forEach(file => {
                allFiles.push({
                    ...file,
                    presensi_id: entry.presensi_id,
                    nama: entry.nama,
                    tanggal: entry.tanggal
                });
            });
        });
        
        document.getElementById('statusScan').innerHTML = `<i class="fas fa-spinner fa-spin"></i> Memindai ${allFiles.length} foto...`;
        
        // Scan setiap foto
        const results = [];
        const progressBar = document.querySelector('#progressScan .progress-bar');
        
        for (let i = 0; i < allFiles.length; i++) {
            const file = allFiles[i];
            const result = await detectFaceInImage(file.url);
            
            results.push({
                ...file,
                ...result
            });
            
            // Update progress
            const progress = ((i + 1) / allFiles.length) * 100;
            progressBar.style.width = progress + '%';
            progressBar.textContent = Math.round(progress) + '%';
            
            // Update status setiap 5 foto
            if ((i + 1) % 5 === 0 || i === allFiles.length - 1) {
                document.getElementById('statusScan').innerHTML = `<i class="fas fa-spinner fa-spin"></i> Memindai ${i + 1} dari ${allFiles.length} foto...`;
            }
            
            // Delay kecil untuk tidak membebani browser
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        // Tampilkan hasil
        const noFaceItems = results.filter(r => !r.hasFace);
        
        if (noFaceItems.length === 0) {
            document.getElementById('statusScan').innerHTML = '<i class="fas fa-check-circle text-success"></i> Semua foto terdeteksi wajah!';
        } else {
            document.getElementById('statusScan').innerHTML = `<i class="fas fa-exclamation-triangle text-warning"></i> Ditemukan ${noFaceItems.length} foto tanpa wajah dari ${allFiles.length} total foto.`;
            
            // Tampilkan daftar foto tanpa wajah
            const daftarContainer = document.getElementById('daftarFotoNoFace');
            daftarContainer.innerHTML = '';
            
            noFaceItems.forEach(item => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'card mb-2';
                itemDiv.innerHTML = `
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <img src="${item.url}" class="img-fluid rounded" alt="Foto ${item.jenis_foto}" style="max-height: 100px;">
                            </div>
                            <div class="col-md-9">
                                <h6 class="card-title">${item.nama}</h6>
                                <p class="card-text">
                                    <strong>Tanggal:</strong> ${item.tanggal}<br>
                                    <strong>Jenis:</strong> ${item.jenis_foto === 'masuk' ? 'Masuk' : 'Pulang'}<br>
                                    <strong>Status:</strong> <span class="badge bg-danger">Tidak Ada Wajah</span>
                                    ${item.error ? `<br><strong>Error:</strong> ${item.error}` : ''}
                                </p>
                            </div>
                        </div>
                    </div>
                `;
                daftarContainer.appendChild(itemDiv);
            });
            
            document.getElementById('hasilScan').style.display = 'block';
        }
        
    } catch (error) {
        console.error('Error during scan:', error);
        document.getElementById('statusScan').innerHTML = `<i class="fas fa-exclamation-circle text-danger"></i> Error: ${error.message}`;
    } finally {
        isScanning = false;
        document.getElementById('btnMulaiScan').disabled = false;
    }
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Set bulan dan tahun saat ini
    document.getElementById('scanBulan').value = <?php echo date('n'); ?>;
    document.getElementById('scanTahun').value = <?php echo date('Y'); ?>;
    
    // Button scan foto
    document.getElementById('btnScanFoto').addEventListener('click', function() {
        const modal = new bootstrap.Modal(document.getElementById('modalScanFoto'));
        modal.show();
    });
    
    // Button mulai scan
    document.getElementById('btnMulaiScan').addEventListener('click', mulaiScanFoto);
});
</script>
