<?php
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Set header JSON
header('Content-Type: application/json');

// Cek akses admin
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'Aks<PERSON> ditolak']);
    exit;
}

// Validasi parameter ID
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'ID karyawan tidak valid']);
    exit;
}

$id = clean($_GET['id']);

// Ambil data karyawan untuk validasi
$query = "SELECT * FROM users WHERE id = '$id' AND role = 'karyawan'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    echo json_encode(['success' => false, 'message' => 'Karyawan tidak ditemukan']);
    exit;
}

$karyawan = mysqli_fetch_assoc($result);
$data_count = [];

try {
    // Cek data presensi
    $query = "SELECT COUNT(*) as total FROM presensi WHERE user_id = '$id'";
    $result = mysqli_query($conn, $query);
    if ($result) {
        $count = mysqli_fetch_assoc($result)['total'];
        if ($count > 0) {
            $data_count[] = "$count data presensi";
        }
    }
    
    // Cek data deteksi wajah
    $query = "SELECT COUNT(*) as total FROM deteksi_wajah WHERE user_id = '$id'";
    $result = mysqli_query($conn, $query);
    if ($result) {
        $count = mysqli_fetch_assoc($result)['total'];
        if ($count > 0) {
            $data_count[] = "$count data deteksi wajah";
        }
    }
    
    // Cek data aktivitas karyawan (jika tabel ada)
    $query = "SHOW TABLES LIKE 'aktivitas_karyawan'";
    $result = mysqli_query($conn, $query);
    if (mysqli_num_rows($result) > 0) {
        $query = "SELECT COUNT(*) as total FROM aktivitas_karyawan WHERE user_id = '$id'";
        $result = mysqli_query($conn, $query);
        if ($result) {
            $count = mysqli_fetch_assoc($result)['total'];
            if ($count > 0) {
                $data_count[] = "$count data aktivitas";
            }
        }
    }
    
    // Cek data izin dinas (jika tabel ada)
    $query = "SHOW TABLES LIKE 'izin_dinas'";
    $result = mysqli_query($conn, $query);
    if (mysqli_num_rows($result) > 0) {
        $query = "SELECT COUNT(*) as total FROM izin_dinas WHERE user_id = '$id'";
        $result = mysqli_query($conn, $query);
        if ($result) {
            $count = mysqli_fetch_assoc($result)['total'];
            if ($count > 0) {
                $data_count[] = "$count data izin dinas";
            }
        }
    }
    
    // Cek data gangguan absensi (jika tabel ada)
    $query = "SHOW TABLES LIKE 'gangguan_absensi'";
    $result = mysqli_query($conn, $query);
    if (mysqli_num_rows($result) > 0) {
        $query = "SELECT COUNT(*) as total FROM gangguan_absensi WHERE user_id = '$id'";
        $result = mysqli_query($conn, $query);
        if ($result) {
            $count = mysqli_fetch_assoc($result)['total'];
            if ($count > 0) {
                $data_count[] = "$count data gangguan absensi";
            }
        }
    }
    
    // Cek data user_schedule_choices (jika tabel ada)
    $query = "SHOW TABLES LIKE 'user_schedule_choices'";
    $result = mysqli_query($conn, $query);
    if (mysqli_num_rows($result) > 0) {
        $query = "SELECT COUNT(*) as total FROM user_schedule_choices WHERE user_id = '$id'";
        $result = mysqli_query($conn, $query);
        if ($result) {
            $count = mysqli_fetch_assoc($result)['total'];
            if ($count > 0) {
                $data_count[] = "$count data pilihan jadwal";
            }
        }
    }
    
    // Cek data absensi_offline (jika tabel ada)
    $query = "SHOW TABLES LIKE 'absensi_offline'";
    $result = mysqli_query($conn, $query);
    if (mysqli_num_rows($result) > 0) {
        $query = "SELECT COUNT(*) as total FROM absensi_offline WHERE user_id = '$id'";
        $result = mysqli_query($conn, $query);
        if ($result) {
            $count = mysqli_fetch_assoc($result)['total'];
            if ($count > 0) {
                $data_count[] = "$count data absensi offline";
            }
        }
    }
    
    // Cek data laporan harian berdasarkan nama (tidak ada foreign key)
    $query = "SELECT COUNT(*) as total FROM laporan_harian WHERE nama_karyawan = '" . mysqli_real_escape_string($conn, $karyawan['nama']) . "'";
    $result = mysqli_query($conn, $query);
    if ($result) {
        $count = mysqli_fetch_assoc($result)['total'];
        if ($count > 0) {
            $data_count[] = "$count data laporan harian";
        }
    }
    
    // Cek data rapat_peserta (jika tabel ada)
    $query = "SHOW TABLES LIKE 'rapat_peserta'";
    $result = mysqli_query($conn, $query);
    if (mysqli_num_rows($result) > 0) {
        $query = "SELECT COUNT(*) as total FROM rapat_peserta WHERE user_id = '$id'";
        $result = mysqli_query($conn, $query);
        if ($result) {
            $count = mysqli_fetch_assoc($result)['total'];
            if ($count > 0) {
                $data_count[] = "$count data keikutsertaan rapat";
            }
        }
    }
    
    // Cek data lembur (jika tabel ada)
    $query = "SHOW TABLES LIKE 'lembur'";
    $result = mysqli_query($conn, $query);
    if (mysqli_num_rows($result) > 0) {
        $query = "SELECT COUNT(*) as total FROM lembur WHERE user_id = '$id'";
        $result = mysqli_query($conn, $query);
        if ($result) {
            $count = mysqli_fetch_assoc($result)['total'];
            if ($count > 0) {
                $data_count[] = "$count data lembur";
            }
        }
    }
    
    // Response sukses
    echo json_encode([
        'success' => true,
        'employee' => [
            'id' => $karyawan['id'],
            'nik' => $karyawan['nik'],
            'nama' => $karyawan['nama']
        ],
        'data_count' => $data_count,
        'total_data' => count($data_count)
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
}
?>
