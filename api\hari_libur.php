<?php
require_once '../config/database.php';
require_once '../config/config.php';

// CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

function validateApiKey($api_key) {
    return $api_key === 'absensiku_api_key_2023';
}

$method = $_SERVER['REQUEST_METHOD'];
$input = file_get_contents('php://input');
$data = json_decode($input, true);
if (!$data) $data = $_POST;

// Untuk GET, ambil api_key dari $_GET jika tidak ada di body
if ($method === 'GET') {
    if (!isset($data['api_key']) && isset($_GET['api_key'])) {
        $data['api_key'] = $_GET['api_key'];
    }
}

if (!isset($data['api_key']) || !validateApiKey($data['api_key'])) {
    echo json_encode(['status' => 'error', 'message' => 'API key tidak valid']);
    exit;
}

// GET: List/filter hari libur
if ($method === 'GET') {
    $where = [];
    if (isset($_GET['id'])) $where[] = "id = '" . mysqli_real_escape_string($conn, $_GET['id']) . "'";
    if (isset($_GET['tanggal'])) $where[] = "tanggal = '" . mysqli_real_escape_string($conn, $_GET['tanggal']) . "'";
    if (isset($_GET['nama_libur'])) $where[] = "nama_libur LIKE '%" . mysqli_real_escape_string($conn, $_GET['nama_libur']) . "%'";
    $sql = "SELECT id, nama_libur, tanggal, created_at, updated_at FROM hari_libur";
    if ($where) $sql .= " WHERE " . implode(' AND ', $where);
    $sql .= " ORDER BY tanggal DESC";
    $result = mysqli_query($conn, $sql);
    $rows = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $rows[] = $row;
        }
    }
    echo json_encode(['status' => 'success', 'data' => $rows]);
    exit;
}

// POST: Tambah hari libur
if ($method === 'POST') {
    $nama_libur = mysqli_real_escape_string($conn, $data['nama_libur'] ?? '');
    $tanggal = mysqli_real_escape_string($conn, $data['tanggal'] ?? '');
    $created_at = date('Y-m-d H:i:s');
    $updated_at = $created_at;
    $sql = "INSERT INTO hari_libur (nama_libur, tanggal, created_at, updated_at) VALUES ('$nama_libur', '$tanggal', '$created_at', '$updated_at')";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Hari libur berhasil ditambahkan']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal menambah hari libur: ' . mysqli_error($conn)]);
    }
    exit;
}

// PUT: Edit hari libur
if ($method === 'PUT') {
    parse_str(file_get_contents('php://input'), $put_vars);
    $id = mysqli_real_escape_string($conn, $data['id'] ?? $put_vars['id'] ?? '');
    if (!$id) {
        echo json_encode(['status' => 'error', 'message' => 'ID hari libur wajib diisi']);
        exit;
    }
    $fields = [];
    foreach ([
        'nama_libur', 'tanggal'
    ] as $col) {
        $val = $data[$col] ?? $put_vars[$col] ?? null;
        if (!is_null($val)) {
            $fields[] = "$col = '" . mysqli_real_escape_string($conn, $val) . "'";
        }
    }
    $fields[] = "updated_at = '" . date('Y-m-d H:i:s') . "'";
    $sql = "UPDATE hari_libur SET " . implode(', ', $fields) . " WHERE id = '$id'";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Hari libur berhasil diupdate']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal update hari libur: ' . mysqli_error($conn)]);
    }
    exit;
}

// DELETE: Hapus hari libur
if ($method === 'DELETE') {
    parse_str(file_get_contents('php://input'), $del_vars);
    $id = mysqli_real_escape_string($conn, $data['id'] ?? $del_vars['id'] ?? '');
    if (!$id) {
        echo json_encode(['status' => 'error', 'message' => 'ID hari libur wajib diisi']);
        exit;
    }
    $sql = "DELETE FROM hari_libur WHERE id = '$id'";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Hari libur berhasil dihapus']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal menghapus hari libur: ' . mysqli_error($conn)]);
    }
    exit;
}

echo json_encode(['status' => 'error', 'message' => 'Metode tidak didukung']); 