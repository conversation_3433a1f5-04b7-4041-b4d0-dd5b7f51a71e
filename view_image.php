<?php
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Cek apakah ada parameter yang diperlukan
if (!isset($_GET['type']) || !isset($_GET['path']) || !isset($_GET['title'])) {
    die('Parameter tidak lengkap');
}

$type = clean($_GET['type']);
$path = clean($_GET['path']);
$title = clean($_GET['title']);

// Validasi path untuk keamanan
$allowed_types = ['gangguan_absensi', 'izin_dinas'];
if (!in_array($type, $allowed_types)) {
    die('Tipe tidak valid');
}

// Tentukan path lengkap gambar
$image_path = '';
if ($type === 'gangguan_absensi') {
    $image_path = 'uploads/gangguan_absensi/' . $path;
} else {
    $image_path = 'uploads/' . $path;
}

// Cek apakah file ada
if (!file_exists($image_path)) {
    die('File tidak ditemukan');
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px;
        }
        .image-container {
            max-width: 100%;
            margin: 0 auto;
            text-align: center;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .back-button {
            margin-bottom: 20px;
        }
        .image-title {
            margin: 20px 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <button onclick="window.history.back()" class="btn btn-secondary back-button">
            <i class="fas fa-arrow-left"></i> Kembali
        </button>
        
        <h4 class="image-title"><?php echo $title; ?></h4>
        
        <div class="image-container">
            <img src="<?php echo BASE_URL . $image_path; ?>" alt="<?php echo $title; ?>">
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
</body>
</html> 