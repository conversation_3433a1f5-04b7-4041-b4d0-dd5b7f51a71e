<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Proses tambah/edit pengumuman
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        $judul = clean($_POST['judul']);
        $tanggal = clean($_POST['tanggal']);
        $tanggal_kadaluarsa = clean($_POST['tanggal_kadaluarsa']);
        $foto = '';
        
        // Upload foto jika ada
        if (isset($_FILES['foto']) && $_FILES['foto']['error'] == 0) {
            $allowed = ['jpg', 'jpeg', 'png'];
            $filename = $_FILES['foto']['name'];
            $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            
            if (in_array($ext, $allowed)) {
                $new_filename = uniqid() . '.' . $ext;
                $upload_path = '../uploads/pengumuman/' . $new_filename;
                
                // Buat direktori jika belum ada
                if (!file_exists('../uploads/pengumuman')) {
                    mkdir('../uploads/pengumuman', 0777, true);
                }
                
                if (move_uploaded_file($_FILES['foto']['tmp_name'], $upload_path)) {
                    $foto = $new_filename;
                }
            }
        }
        
        if ($_POST['action'] == 'add') {
            $query = "INSERT INTO pengumuman (judul, tanggal, tanggal_kadaluarsa, foto, created_at) 
                     VALUES ('$judul', '$tanggal', '$tanggal_kadaluarsa', '$foto', NOW())";
            
            if (mysqli_query($conn, $query)) {
                $_SESSION['success_message'] = 'Pengumuman berhasil ditambahkan';
            } else {
                $_SESSION['error_message'] = 'Gagal menambahkan pengumuman: ' . mysqli_error($conn);
            }
        } elseif ($_POST['action'] == 'edit') {
            $id = clean($_POST['id']);
            $foto_query = '';
            
            if ($foto) {
                // Hapus foto lama jika ada
                $query = "SELECT foto FROM pengumuman WHERE id = '$id'";
                $result = mysqli_query($conn, $query);
                if ($row = mysqli_fetch_assoc($result)) {
                    if ($row['foto'] && file_exists('../uploads/pengumuman/' . $row['foto'])) {
                        unlink('../uploads/pengumuman/' . $row['foto']);
                    }
                }
                $foto_query = ", foto = '$foto'";
            }
            
            $query = "UPDATE pengumuman 
                     SET judul = '$judul', 
                         tanggal = '$tanggal', 
                         tanggal_kadaluarsa = '$tanggal_kadaluarsa' 
                         $foto_query 
                     WHERE id = '$id'";
            
            if (mysqli_query($conn, $query)) {
                $_SESSION['success_message'] = 'Pengumuman berhasil diperbarui';
            } else {
                $_SESSION['error_message'] = 'Gagal memperbarui pengumuman: ' . mysqli_error($conn);
            }
        }
        
        redirect('admin/pengumuman.php');
    }
}

// Proses hapus pengumuman
if (isset($_GET['delete'])) {
    $id = clean($_GET['delete']);
    
    // Hapus foto jika ada
    $query = "SELECT foto FROM pengumuman WHERE id = '$id'";
    $result = mysqli_query($conn, $query);
    if ($row = mysqli_fetch_assoc($result)) {
        if ($row['foto'] && file_exists('../uploads/pengumuman/' . $row['foto'])) {
            unlink('../uploads/pengumuman/' . $row['foto']);
        }
    }
    
    $query = "DELETE FROM pengumuman WHERE id = '$id'";
    if (mysqli_query($conn, $query)) {
        $_SESSION['success_message'] = 'Pengumuman berhasil dihapus';
    } else {
        $_SESSION['error_message'] = 'Gagal menghapus pengumuman: ' . mysqli_error($conn);
    }
    
    redirect('admin/pengumuman.php');
}

// Ambil data pengumuman
$today = date('Y-m-d');
$query = "UPDATE pengumuman 
        SET status = 'kadaluarsa' 
        WHERE tanggal_kadaluarsa < '$today' 
        AND status = 'aktif'";
mysqli_query($conn, $query);

$query = "SELECT * FROM pengumuman ORDER BY tanggal DESC";
$result = mysqli_query($conn, $query);
$pengumuman_list = [];
while ($row = mysqli_fetch_assoc($result)) {
    $pengumuman_list[] = $row;
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Kelola Pengumuman</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPengumumanModal">
            <i class="fas fa-plus"></i> Tambah Pengumuman
        </button>
    </div>

    <?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php 
        echo $_SESSION['success_message'];
        unset($_SESSION['success_message']);
        ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php 
        echo $_SESSION['error_message'];
        unset($_SESSION['error_message']);
        ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <!-- Data Table -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Judul</th>
                            <th>Tanggal</th>
                            <th>Tanggal Kadaluarsa</th>
                            <th>Status</th>
                            <th>Foto</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($pengumuman_list as $index => $pengumuman): ?>
                        <tr>
                            <td><?php echo $index + 1; ?></td>
                            <td><?php echo $pengumuman['judul']; ?></td>
                            <td><?php echo date('d/m/Y', strtotime($pengumuman['tanggal'])); ?></td>
                            <td><?php echo date('d/m/Y', strtotime($pengumuman['tanggal_kadaluarsa'])); ?></td>
                            <td>
                                <?php if ($pengumuman['status'] == 'aktif'): ?>
                                <span class="badge bg-success">Aktif</span>
                                <?php else: ?>
                                <span class="badge bg-danger">Kadaluarsa</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($pengumuman['foto']): ?>
                                <a href="<?php echo BASE_URL; ?>uploads/pengumuman/<?php echo $pengumuman['foto']; ?>" 
                                   data-fancybox="gallery" 
                                   data-caption="<?php echo $pengumuman['judul']; ?>">
                                    <img src="<?php echo BASE_URL; ?>uploads/pengumuman/<?php echo $pengumuman['foto']; ?>" 
                                         alt="Foto Pengumuman" 
                                         style="max-width: 100px; max-height: 100px; object-fit: cover;">
                                </a>
                                <?php else: ?>
                                <span class="text-muted">Tidak ada foto</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary edit-pengumuman" 
                                        data-id="<?php echo $pengumuman['id']; ?>"
                                        data-judul="<?php echo htmlspecialchars($pengumuman['judul']); ?>"
                                        data-tanggal="<?php echo $pengumuman['tanggal']; ?>"
                                        data-tanggal-kadaluarsa="<?php echo $pengumuman['tanggal_kadaluarsa']; ?>">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <a href="?delete=<?php echo $pengumuman['id']; ?>" 
                                   class="btn btn-sm btn-danger"
                                   onclick="return confirm('Apakah Anda yakin ingin menghapus pengumuman ini?')">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Pengumuman -->
<div class="modal fade" id="addPengumumanModal" tabindex="-1" aria-labelledby="addPengumumanModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addPengumumanModalLabel">Tambah Pengumuman</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">
                    <div class="mb-3">
                        <label for="judul" class="form-label">Judul Pengumuman</label>
                        <input type="text" class="form-control" id="judul" name="judul" required>
                    </div>
                    <div class="mb-3">
                        <label for="tanggal" class="form-label">Tanggal</label>
                        <input type="date" class="form-control" id="tanggal" name="tanggal" required>
                    </div>
                    <div class="mb-3">
                        <label for="tanggal_kadaluarsa" class="form-label">Tanggal Kadaluarsa</label>
                        <input type="date" class="form-control" id="tanggal_kadaluarsa" name="tanggal_kadaluarsa" required>
                    </div>
                    <div class="mb-3">
                        <label for="foto" class="form-label">Foto</label>
                        <input type="file" class="form-control" id="foto" name="foto" accept="image/*">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit Pengumuman -->
<div class="modal fade" id="editPengumumanModal" tabindex="-1" aria-labelledby="editPengumumanModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPengumumanModalLabel">Edit Pengumuman</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="id" id="edit_id">
                    <div class="mb-3">
                        <label for="edit_judul" class="form-label">Judul Pengumuman</label>
                        <input type="text" class="form-control" id="edit_judul" name="judul" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_tanggal" class="form-label">Tanggal</label>
                        <input type="date" class="form-control" id="edit_tanggal" name="tanggal" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_tanggal_kadaluarsa" class="form-label">Tanggal Kadaluarsa</label>
                        <input type="date" class="form-control" id="edit_tanggal_kadaluarsa" name="tanggal_kadaluarsa" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_foto" class="form-label">Foto</label>
                        <input type="file" class="form-control" id="edit_foto" name="foto" accept="image/*">
                        <small class="text-muted">Biarkan kosong jika tidak ingin mengubah foto</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inisialisasi DataTable
    $('#dataTable').DataTable({
        "order": [[3, "desc"]], // Urutkan berdasarkan tanggal kadaluarsa
        "columnDefs": [
            { "orderable": false, "targets": [5, 6] } // Kolom foto dan aksi tidak bisa diurutkan
        ]
    });

    // Event listener untuk tombol edit
    document.querySelectorAll('.edit-pengumuman').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.dataset.id;
            const judul = this.dataset.judul;
            const tanggal = this.dataset.tanggal;
            const tanggalKadaluarsa = this.dataset.tanggalKadaluarsa;

            document.getElementById('edit_id').value = id;
            document.getElementById('edit_judul').value = judul;
            document.getElementById('edit_tanggal').value = tanggal;
            document.getElementById('edit_tanggal_kadaluarsa').value = tanggalKadaluarsa;

            const editModal = new bootstrap.Modal(document.getElementById('editPengumumanModal'));
            editModal.show();
        });
    });

    // Validasi tanggal kadaluarsa harus lebih besar dari tanggal
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const tanggal = form.querySelector('input[name="tanggal"]').value;
            const tanggalKadaluarsa = form.querySelector('input[name="tanggal_kadaluarsa"]').value;

            if (tanggalKadaluarsa < tanggal) {
                e.preventDefault();
                alert('Tanggal kadaluarsa harus lebih besar dari tanggal pengumuman!');
            }
        });
    });
});
</script>

<style>
.badge {
    font-size: 12px;
    padding: 5px 10px;
    border-radius: 4px;
}

.bg-success {
    background-color: #1cc88a !important;
}

.bg-danger {
    background-color: #e74a3b !important;
}
</style>

<?php
// Include footer
include_once '../includes/footer.php';
?> 