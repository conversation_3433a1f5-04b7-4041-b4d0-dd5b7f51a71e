<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

$response = [
    'success' => false,
    'message' => ''
];

// Pastikan request adalah POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Ambil data dari body request
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if ($data) {
        $id = isset($data['id']) ? clean($data['id']) : null;
        $type = isset($data['type']) ? clean($data['type']) : null;
        $approver_id = isset($data['approver_id']) ? clean($data['approver_id']) : null;

        if ($id && $type && $approver_id) {
            // Proses berdasarkan tipe
            if ($type == 'izin_dinas') {
                $query = "UPDATE izin_dinas SET 
                          status = 'Rejected', 
                          approved_by = '$approver_id', 
                          approved_at = NOW() 
                          WHERE id = '$id'";
            } else if ($type == 'gangguan_absensi') {
                $query = "UPDATE gangguan_absensi SET 
                          status = 'Rejected', 
                          approved_by = '$approver_id', 
                          approved_at = NOW() 
                          WHERE id = '$id'";
            } else {
                $response['message'] = 'Tipe pengajuan tidak valid';
                echo json_encode($response);
                exit;
            }

            if (mysqli_query($conn, $query)) {
                $response['success'] = true;
                $response['message'] = 'Pengajuan berhasil ditolak';
            } else {
                $response['message'] = 'Gagal menolak pengajuan: ' . mysqli_error($conn);
            }
        } else {
            $response['message'] = 'ID, tipe, dan approver_id diperlukan';
        }
    } else {
        $response['message'] = 'Data tidak valid';
    }
} else {
    $response['message'] = 'Metode request tidak diizinkan';
}

echo json_encode($response);
?> 