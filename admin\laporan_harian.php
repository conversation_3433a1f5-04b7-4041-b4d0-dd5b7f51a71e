<?php
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

checkAccess('admin');

// Proses export ke Excel
if (isset($_GET['export']) && $_GET['export'] == 'excel') {
    $nama_karyawan = isset($_GET['nama_karyawan']) ? clean($_GET['nama_karyawan']) : '';
    $periode = isset($_GET['periode']) ? clean($_GET['periode']) : '';

    if (empty($nama_karyawan) || empty($periode)) {
        $_SESSION['error'] = 'Nama karyawan dan periode harus dipilih untuk export Excel!';
        header('Location: laporan_harian.php');
        exit;
    }

    // Set header untuk download file Excel
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_' . str_replace(' ', '_', $nama_karyawan) . '_' . $periode . '.xls"');
    header('Cache-Control: max-age=0');

    // Query untuk mengambil data berdasarkan filter
    $query = "SELECT * FROM laporan_harian
              WHERE nama_karyawan = '" . mysqli_real_escape_string($conn, $nama_karyawan) . "'
              AND DATE_FORMAT(tanggal, '%Y-%m') = '" . mysqli_real_escape_string($conn, $periode) . "'
              ORDER BY tanggal ASC";
    $result = mysqli_query($conn, $query);

    // Output tabel Excel dengan styling yang diperbaiki
    echo '<html>';
    echo '<head>';
    echo '<meta charset="UTF-8">';
    echo '<style>';
    echo 'table { border-collapse: collapse; width: 100%; table-layout: fixed; }';
    echo 'th, td { border: 1px solid #000; padding: 4px; text-align: left; vertical-align: middle; overflow: hidden; }';
    echo 'th { background-color: #f2f2f2; font-weight: bold; text-align: center; }';
    echo '.header { background-color: #4CAF50; color: white; text-align: center; font-size: 16px; font-weight: bold; }';
    echo '.info { background-color: #e7f3ff; text-align: center; font-weight: bold; }';
    echo '.foto-cell { width: 120px !important; height: 90px !important; text-align: center; padding: 2px !important; }';
    echo '.foto-img { width: 110px !important; height: 80px !important; object-fit: cover; border: 1px solid #ddd; display: block; margin: 0 auto; }';
    echo '.no-foto { width: 110px; height: 80px; border: 1px dashed #ccc; display: flex; align-items: center; justify-content: center; color: #666; background-color: #f9f9f9; font-size: 10px; margin: 0 auto; }';
    echo '.no-cell { width: 30px; text-align: center; }';
    echo '.date-cell { width: 80px; font-size: 11px; }';
    echo '.keterangan-cell { width: 250px; word-wrap: break-word; font-size: 11px; }';
    echo '.link-cell { width: 100px; font-size: 10px; }';
    echo '</style>';
    echo '</head>';
    echo '<body>';

    echo '<table>';
    echo '<tr>';
    echo '<th colspan="7" class="header">LAPORAN KINERJA HARIAN</th>';
    echo '</tr>';
    echo '<tr>';
    echo '<th colspan="7" class="info">Nama Karyawan: ' . htmlspecialchars($nama_karyawan) . '</th>';
    echo '</tr>';
    echo '<tr>';
    echo '<th colspan="7" class="info">Periode: ' . date('F Y', strtotime($periode . '-01')) . '</th>';
    echo '</tr>';
    echo '<tr>';
    echo '<th class="no-cell">No</th>';
    echo '<th class="date-cell">Tanggal</th>';
    echo '<th class="date-cell">Periode</th>';
    echo '<th class="keterangan-cell">Keterangan</th>';
    echo '<th class="foto-cell">Foto</th>';
    echo '<th class="link-cell">Link Foto</th>';
    echo '<th class="date-cell">Waktu Input</th>';
    echo '</tr>';

    $no = 1;
    if (mysqli_num_rows($result) > 0) {
        while ($row = mysqli_fetch_assoc($result)) {
            echo '<tr style="height: 90px;">';
            echo '<td class="no-cell">' . $no++ . '</td>';
            echo '<td class="date-cell">' . date('d/m/Y', strtotime($row['tanggal'])) . '</td>';
            echo '<td class="date-cell">' . htmlspecialchars($row['periode']) . '</td>';
            echo '<td class="keterangan-cell">' . nl2br(htmlspecialchars($row['keterangan'])) . '</td>';

            // Kolom foto dengan ukuran tetap
            echo '<td class="foto-cell">';
            if ($row['foto'] && file_exists('../uploads/' . $row['foto'])) {
                $foto_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/../uploads/' . $row['foto'];
                echo '<img src="' . $foto_url . '" alt="Foto" class="foto-img" />';
            } else {
                echo '<div class="no-foto">No Photo</div>';
            }
            echo '</td>';

            // Kolom link foto untuk backup
            echo '<td class="link-cell">';
            if ($row['foto']) {
                $foto_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/../uploads/' . $row['foto'];
                echo '<div style="font-size: 9px;"><a href="' . $foto_url . '" target="_blank">' . htmlspecialchars($row['foto']) . '</a></div>';
                echo '<div style="font-size: 8px; color: #666;">' . (file_exists('../uploads/' . $row['foto']) ? '✓ Ada' : '✗ Hilang') . '</div>';
            } else {
                echo '<em style="color: #999; font-size: 9px;">No File</em>';
            }
            echo '</td>';

            echo '<td class="date-cell">' . date('d/m/Y<br/>H:i', strtotime($row['created_at'])) . '</td>';
            echo '</tr>';
        }
    } else {
        echo '<tr>';
        echo '<td colspan="7" style="text-align: center; padding: 20px; color: #666;">Tidak ada data laporan untuk karyawan dan periode yang dipilih</td>';
        echo '</tr>';
    }

    echo '</table>';
    echo '</body>';
    echo '</html>';
    exit;
}

// Ambil semua data laporan
$query = "SELECT * FROM laporan_harian ORDER BY tanggal DESC";
$result = mysqli_query($conn, $query);

// Ambil nama-nama karyawan unik
$karyawan_result = mysqli_query($conn, "SELECT DISTINCT nama_karyawan FROM laporan_harian ORDER BY nama_karyawan ASC");

// Ambil periode (bulan-tahun) unik dari tanggal
$periode_result = mysqli_query($conn, "
    SELECT DISTINCT DATE_FORMAT(tanggal, '%Y-%m') AS periode
    FROM laporan_harian
    ORDER BY periode DESC
");

// Fungsi untuk ubah format bulan
function formatPeriode($ym) {
    $bulanMap = [
        '01'=>'Januari','02'=>'Februari','03'=>'Maret','04'=>'April','05'=>'Mei','06'=>'Juni',
        '07'=>'Juli','08'=>'Agustus','09'=>'September','10'=>'Oktober','11'=>'November','12'=>'Desember'
    ];
    [$tahun, $bulan] = explode('-', $ym);
    return $bulanMap[$bulan] . ' ' . $tahun;
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Laporan Harian</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item active">Laporan Harian</li>
    </ol>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Filter dan Export -->
    <div class="row mb-3">
        <div class="col-md-4">
            <label for="filterNama" class="form-label">Filter Nama Karyawan:</label>
            <select class="form-select" id="filterNama">
                <option value="">-- Semua Nama --</option>
                <?php while ($row = mysqli_fetch_assoc($karyawan_result)) {
                    echo '<option value="' . htmlspecialchars($row['nama_karyawan']) . '">' . htmlspecialchars($row['nama_karyawan']) . '</option>';
                } ?>
            </select>
        </div>
        <div class="col-md-4">
            <label for="filterPeriode" class="form-label">Filter Periode Bulan:</label>
            <select class="form-select" id="filterPeriode">
                <option value="">-- Semua Periode --</option>
                <?php while ($row = mysqli_fetch_assoc($periode_result)) {
                    $periode = $row['periode']; // contoh: 2025-01
                    echo '<option value="' . $periode . '">' . formatPeriode($periode) . '</option>';
                } ?>
            </select>
        </div>
        <div class="col-md-4">
            <label class="form-label">Export Excel:</label>
            <div class="btn-group-vertical d-grid gap-1">
                <button type="button" class="btn btn-success btn-sm" id="exportExcel" disabled>
                    <i class="fas fa-file-excel"></i> Export XLS (Standar)
                </button>
                <button type="button" class="btn btn-primary btn-sm" id="exportExcelFoto" disabled>
                    <i class="fas fa-images"></i> Export HTML (Dengan Foto)
                </button>
                <button type="button" class="btn btn-info btn-sm" id="exportExcelXlsx" disabled>
                    <i class="fas fa-file-excel"></i> Export XLSX (Professional)
                </button>
                <small class="form-text text-muted">Pilih nama karyawan dan periode untuk export</small>
            </div>
        </div>
    </div>

    <!-- Tabel Laporan -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-book me-1"></i>
            Daftar Laporan Harian
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="laporanTable" class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Nama Karyawan</th>
                            <th>Periode</th>
                            <th>Tanggal</th>
                            <th>Keterangan</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $no = 1;
                        mysqli_data_seek($result, 0); // reset pointer
                        while ($row = mysqli_fetch_assoc($result)) {
                            $periodeValue = date('Y-m', strtotime($row['tanggal'])); // format untuk filter
                            echo '<tr data-nama="' . htmlspecialchars($row['nama_karyawan']) . '" data-periode="' . $periodeValue . '">
                                <td>' . $no++ . '</td>
                                <td>' . htmlspecialchars($row['nama_karyawan']) . '</td>
                                <td>' . formatPeriode($periodeValue) . '</td>
                                <td>' . date('d/m/Y', strtotime($row['tanggal'])) . '</td>
                                <td>' . htmlspecialchars(substr($row['keterangan'], 0, 50)) . '...</td>
                                <td>
                                    <button class="btn btn-info btn-sm view-detail"
                                            data-id="' . $row['id'] . '"
                                            data-nama="' . htmlspecialchars($row['nama_karyawan']) . '"
                                            data-periode="' . htmlspecialchars($row['periode']) . '"
                                            data-tanggal="' . htmlspecialchars($row['tanggal']) . '"
                                            data-keterangan="' . htmlspecialchars($row['keterangan']) . '"
                                            data-foto="' . htmlspecialchars($row['foto']) . '"
                                            data-created="' . htmlspecialchars($row['created_at']) . '"
                                            data-updated="' . htmlspecialchars($row['updated_at']) . '">
                                        <i class="fas fa-eye"></i> Lihat Detail
                                    </button>
                                </td>
                            </tr>';
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail -->
<div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Detail Laporan Harian</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <dl class="row">
            <dt class="col-sm-3">Nama Karyawan</dt>
            <dd class="col-sm-9" id="detailNama"></dd>

            <dt class="col-sm-3">Periode</dt>
            <dd class="col-sm-9" id="detailPeriode"></dd>

            <dt class="col-sm-3">Tanggal</dt>
            <dd class="col-sm-9" id="detailTanggal"></dd>

            <dt class="col-sm-3">Keterangan</dt>
            <dd class="col-sm-9" id="detailKeterangan"></dd>

            <dt class="col-sm-3">Foto</dt>
            <dd class="col-sm-9" id="detailFoto"></dd>

            <dt class="col-sm-3">Dibuat</dt>
            <dd class="col-sm-9" id="detailCreated"></dd>

            <dt class="col-sm-3">Diperbarui</dt>
            <dd class="col-sm-9" id="detailUpdated"></dd>
        </dl>
      </div>
    </div>
  </div>
</div>

<?php include_once '../includes/footer.php'; ?>

<script>
$(document).ready(function () {
    const table = $('#laporanTable').DataTable();

    function filterTabel() {
        const nama = $('#filterNama').val();
        const periode = $('#filterPeriode').val();

        table.rows().every(function () {
            const row = $(this.node());
            const rowNama = row.data('nama');
            const rowPeriode = row.data('periode');
            const matchNama = !nama || rowNama === nama;
            const matchPeriode = !periode || rowPeriode === periode;

            if (matchNama && matchPeriode) {
                row.show();
            } else {
                row.hide();
            }
        });
    }

    $('#filterNama, #filterPeriode').on('change', function() {
        filterTabel();
        checkExportButton();
    });

    // Fungsi untuk mengecek apakah tombol export bisa diaktifkan
    function checkExportButton() {
        const nama = $('#filterNama').val();
        const periode = $('#filterPeriode').val();

        if (nama && periode) {
            $('#exportExcel').prop('disabled', false);
            $('#exportExcelFoto').prop('disabled', false);
            $('#exportExcelXlsx').prop('disabled', false);
        } else {
            $('#exportExcel').prop('disabled', true);
            $('#exportExcelFoto').prop('disabled', true);
            $('#exportExcelXlsx').prop('disabled', true);
        }
    }

    // Event handler untuk tombol export Excel standar
    $('#exportExcel').click(function() {
        const nama = $('#filterNama').val();
        const periode = $('#filterPeriode').val();

        if (nama && periode) {
            // Redirect ke URL export dengan parameter
            window.location.href = 'laporan_harian.php?export=excel&nama_karyawan=' +
                                   encodeURIComponent(nama) + '&periode=' + encodeURIComponent(periode);
        } else {
            alert('Silakan pilih nama karyawan dan periode terlebih dahulu!');
        }
    });

    // Event handler untuk tombol export Excel dengan foto
    $('#exportExcelFoto').click(function() {
        const nama = $('#filterNama').val();
        const periode = $('#filterPeriode').val();

        if (nama && periode) {
            // Redirect ke URL export foto dengan parameter
            window.location.href = 'export_laporan_harian_foto.php?nama_karyawan=' +
                                   encodeURIComponent(nama) + '&periode=' + encodeURIComponent(periode);
        } else {
            alert('Silakan pilih nama karyawan dan periode terlebih dahulu!');
        }
    });

    // Event handler untuk tombol export XLSX
    $('#exportExcelXlsx').click(function() {
        const nama = $('#filterNama').val();
        const periode = $('#filterPeriode').val();

        if (nama && periode) {
            // Redirect ke URL export XLSX dengan parameter
            window.location.href = 'export_laporan_harian_xlsx.php?nama_karyawan=' +
                                   encodeURIComponent(nama) + '&periode=' + encodeURIComponent(periode);
        } else {
            alert('Silakan pilih nama karyawan dan periode terlebih dahulu!');
        }
    });

    $('.view-detail').click(function () {
        $('#detailNama').text($(this).data('nama'));
        $('#detailPeriode').text($(this).data('periode'));
        $('#detailTanggal').text($(this).data('tanggal'));
        $('#detailKeterangan').text($(this).data('keterangan'));

        let foto = $(this).data('foto');
        if (foto) {
            $('#detailFoto').html('<img src="../uploads/' + foto + '" alt="Foto Laporan" class="img-fluid rounded" style="max-height:300px;">');
        } else {
            $('#detailFoto').text('Tidak ada foto');
        }

        $('#detailCreated').text($(this).data('created'));
        $('#detailUpdated').text($(this).data('updated'));
        $('#detailModal').modal('show');
    });
});
</script>
