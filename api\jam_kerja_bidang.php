<?php
require_once '../config/database.php';
require_once '../config/config.php';

// CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

function validateApiKey($api_key) {
    return $api_key === 'absensiku_api_key_2023';
}

$method = $_SERVER['REQUEST_METHOD'];
$input = file_get_contents('php://input');
$data = json_decode($input, true);
if (!$data) $data = $_POST;

// Untuk GET, ambil api_key dari $_GET jika tidak ada di body
if ($method === 'GET') {
    if (!isset($data['api_key']) && isset($_GET['api_key'])) {
        $data['api_key'] = $_GET['api_key'];
    }
}

if (!isset($data['api_key']) || !validateApiKey($data['api_key'])) {
    echo json_encode(['status' => 'error', 'message' => 'API key tidak valid']);
    exit;
}

// GET: List/filter jam_kerja_bidang
if ($method === 'GET') {
    $where = [];
    if (isset($_GET['id'])) $where[] = "id = '" . mysqli_real_escape_string($conn, $_GET['id']) . "'";
    if (isset($_GET['bidang_id'])) $where[] = "bidang_id = '" . mysqli_real_escape_string($conn, $_GET['bidang_id']) . "'";
    if (isset($_GET['hari'])) $where[] = "hari = '" . mysqli_real_escape_string($conn, $_GET['hari']) . "'";
    if (isset($_GET['jam_kerja_id'])) $where[] = "jam_kerja_id = '" . mysqli_real_escape_string($conn, $_GET['jam_kerja_id']) . "'";
    $sql = "SELECT id, bidang_id, hari, jam_kerja_id, created_at, updated_at FROM jam_kerja_bidang";
    if ($where) $sql .= " WHERE " . implode(' AND ', $where);
    $sql .= " ORDER BY bidang_id ASC, hari ASC";
    $result = mysqli_query($conn, $sql);
    $rows = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $rows[] = $row;
        }
    }
    echo json_encode(['status' => 'success', 'data' => $rows]);
    exit;
}

// POST: Tambah jam_kerja_bidang
if ($method === 'POST') {
    $bidang_id = mysqli_real_escape_string($conn, $data['bidang_id'] ?? '');
    $hari = mysqli_real_escape_string($conn, $data['hari'] ?? '');
    $jam_kerja_id = mysqli_real_escape_string($conn, $data['jam_kerja_id'] ?? '');
    $created_at = date('Y-m-d H:i:s');
    $updated_at = $created_at;
    $sql = "INSERT INTO jam_kerja_bidang (bidang_id, hari, jam_kerja_id, created_at, updated_at) VALUES ('$bidang_id', '$hari', '$jam_kerja_id', '$created_at', '$updated_at')";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Hari kerja berhasil ditambahkan']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal menambah hari kerja: ' . mysqli_error($conn)]);
    }
    exit;
}

// PUT: Edit jam_kerja_bidang
if ($method === 'PUT') {
    parse_str(file_get_contents('php://input'), $put_vars);
    $id = mysqli_real_escape_string($conn, $data['id'] ?? $put_vars['id'] ?? '');
    if (!$id) {
        echo json_encode(['status' => 'error', 'message' => 'ID wajib diisi']);
        exit;
    }
    $fields = [];
    foreach ([
        'bidang_id', 'hari', 'jam_kerja_id'
    ] as $col) {
        $val = $data[$col] ?? $put_vars[$col] ?? null;
        if (!is_null($val)) {
            $fields[] = "$col = '" . mysqli_real_escape_string($conn, $val) . "'";
        }
    }
    $fields[] = "updated_at = '" . date('Y-m-d H:i:s') . "'";
    $sql = "UPDATE jam_kerja_bidang SET " . implode(', ', $fields) . " WHERE id = '$id'";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Hari kerja berhasil diupdate']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal update hari kerja: ' . mysqli_error($conn)]);
    }
    exit;
}

// DELETE: Hapus jam_kerja_bidang
if ($method === 'DELETE') {
    parse_str(file_get_contents('php://input'), $del_vars);
    $id = mysqli_real_escape_string($conn, $data['id'] ?? $del_vars['id'] ?? '');
    if (!$id) {
        echo json_encode(['status' => 'error', 'message' => 'ID wajib diisi']);
        exit;
    }
    $sql = "DELETE FROM jam_kerja_bidang WHERE id = '$id'";
    if (mysqli_query($conn, $sql)) {
        echo json_encode(['status' => 'success', 'message' => 'Hari kerja berhasil dihapus']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Gagal menghapus hari kerja: ' . mysqli_error($conn)]);
    }
    exit;
}

echo json_encode(['status' => 'error', 'message' => 'Metode tidak didukung']); 