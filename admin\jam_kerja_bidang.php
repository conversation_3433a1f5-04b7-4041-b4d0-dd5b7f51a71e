<?php
// Include file konfigurasi
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Cek akses
checkAccess('admin');

// Proses update jam kerja bidang
if (isset($_POST['update'])) {
    $bidang_id = clean($_POST['bidang_id']);

    // Update data jam kerja bidang
    $hari = ['Senin', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Jumat', 'Sabtu', 'Minggu'];

    foreach ($hari as $h) {
        $jam_kerja_id = isset($_POST['jam_kerja'][$h]) ? clean($_POST['jam_kerja'][$h]) : null;

        // Jika jam kerja id kosong, set ke NULL
        if (empty($jam_kerja_id)) {
            $jam_kerja_id = "NULL";
        }

        // Cek apakah data sudah ada
        $check_query = "SELECT id FROM jam_kerja_bidang WHERE bidang_id = '$bidang_id' AND hari = '$h'";
        $check_result = mysqli_query($conn, $check_query);

        if (mysqli_num_rows($check_result) > 0) {
            // Update data jam kerja bidang
            $query = "UPDATE jam_kerja_bidang SET jam_kerja_id = $jam_kerja_id
                      WHERE bidang_id = '$bidang_id' AND hari = '$h'";
            if (!mysqli_query($conn, $query)) {
                error_log("Error updating jam_kerja_bidang: " . mysqli_error($conn) . " - Query: " . $query);
            }
        } else {
            // Insert data jam kerja bidang
            $query = "INSERT INTO jam_kerja_bidang (bidang_id, hari, jam_kerja_id)
                      VALUES ('$bidang_id', '$h', $jam_kerja_id)";
            if (!mysqli_query($conn, $query)) {
                error_log("Error inserting jam_kerja_bidang: " . mysqli_error($conn) . " - Query: " . $query);
            }
        }
    }

    setMessage('success', 'Konfigurasi jam kerja bidang berhasil diperbarui!');
    redirect('admin/jam_kerja_bidang.php?bidang_id=' . $bidang_id);
}

// Cek apakah tabel jam_kerja_bidang sudah ada
$query = "SHOW TABLES LIKE 'jam_kerja_bidang'";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) == 0) {
    setMessage('warning', 'Database perlu diperbarui. Silakan klik tombol Update Database.');
    redirect('admin/update_database.php');
}

// Ambil data bidang
$query = "SELECT * FROM bidang ORDER BY nama_bidang ASC";
$result = mysqli_query($conn, $query);

$bidang = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $bidang[] = $row;
    }
}

// Ambil data bidang yang dipilih
$selected_bidang_id = isset($_GET['bidang_id']) ? clean($_GET['bidang_id']) : (isset($bidang[0]) ? $bidang[0]['id'] : null);

// Ambil data jam kerja
$query = "SELECT * FROM jam_kerja ORDER BY nama_jam_kerja ASC";
$result = mysqli_query($conn, $query);

$jam_kerja = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $jam_kerja[] = $row;
    }
}

// Cek apakah data jam kerja bidang sudah ada untuk bidang yang dipilih
$jam_kerja_bidang = [];
if ($selected_bidang_id) {
    // Cek apakah data sudah ada
    $check_query = "SELECT COUNT(*) as count FROM jam_kerja_bidang WHERE bidang_id = '$selected_bidang_id'";
    $check_result = mysqli_query($conn, $check_query);
    $check_row = mysqli_fetch_assoc($check_result);

    // Jika data belum ada, inisialisasi data jam kerja bidang
    if ($check_row['count'] == 0) {
        $hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];

        foreach ($hari as $h) {
            $query = "INSERT INTO jam_kerja_bidang (bidang_id, hari, jam_kerja_id)
                      VALUES ('$selected_bidang_id', '$h', NULL)";
            mysqli_query($conn, $query);
        }

        // Log inisialisasi
        error_log("Initialized jam_kerja_bidang for bidang_id: $selected_bidang_id");
    }

    // Ambil data jam kerja bidang
    $query = "SELECT jkb.*, jk.nama_jam_kerja, jk.awal_jam_masuk, jk.jam_masuk, jk.akhir_jam_masuk, jk.jam_pulang, jk.akhir_jam_pulang
              FROM jam_kerja_bidang jkb
              LEFT JOIN jam_kerja jk ON jkb.jam_kerja_id = jk.id
              WHERE jkb.bidang_id = '$selected_bidang_id'
              ORDER BY FIELD(jkb.hari, 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu')";
    $result = mysqli_query($conn, $query);

    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $jam_kerja_bidang[$row['hari']] = $row;
        }
    }
}

// Ambil data hari kerja untuk bidang yang dipilih
$hari_kerja = [];
if ($selected_bidang_id) {
    $query = "SELECT * FROM hari_kerja WHERE bidang_id = '$selected_bidang_id' ORDER BY FIELD(hari, 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu')";
    $result = mysqli_query($conn, $query);

    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $hari_kerja[$row['hari']] = $row['status'];
        }
    }
}

// Ambil nama bidang yang dipilih
$selected_bidang_name = '';
foreach ($bidang as $b) {
    if ($b['id'] == $selected_bidang_id) {
        $selected_bidang_name = $b['nama_bidang'];
        break;
    }
}

// Include header
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Konfigurasi Jam Kerja Bidang</h1>
        <div>
            <!--<a href="update_database.php" class="btn btn-warning me-2">-->
            <!--    <i class="fas fa-database"></i> Update Database-->
            <!--</a>-->
            <a href="bidang.php" class="btn btn-info me-2">
                <i class="fas fa-building"></i> Kelola Bidang
            </a>
            <a href="jam_kerja.php" class="btn btn-primary">
                <i class="fas fa-clock"></i> Kelola Jam Kerja
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Pilih Bidang</h6>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <?php foreach ($bidang as $b): ?>
                            <a href="jam_kerja_bidang.php?bidang_id=<?php echo $b['id']; ?>" class="list-group-item list-group-item-action <?php echo ($b['id'] == $selected_bidang_id) ? 'active' : ''; ?>">
                                <?php echo $b['nama_bidang']; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Jam Kerja untuk Bidang: <?php echo $selected_bidang_name; ?></h6>
                </div>
                <div class="card-body">
                    <?php if ($selected_bidang_id): ?>
                        <form method="post" action="">
                            <input type="hidden" name="bidang_id" value="<?php echo $selected_bidang_id; ?>">

                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Hari</th>
                                            <th>Status</th>
                                            <th>Jam Kerja</th>
                                            <th>Detail Jam Kerja</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];
                                        foreach ($hari as $h):
                                            $is_hari_kerja = isset($hari_kerja[$h]) && $hari_kerja[$h];
                                            $jam_kerja_id = isset($jam_kerja_bidang[$h]) ? $jam_kerja_bidang[$h]['jam_kerja_id'] : null;
                                        ?>
                                            <tr>
                                                <td><?php echo $h; ?></td>
                                                <td>
                                                    <?php if ($is_hari_kerja): ?>
                                                        <span class="badge bg-success">Hari Kerja</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Hari Libur</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <select class="form-select" name="jam_kerja[<?php echo $h; ?>]" <?php echo !$is_hari_kerja ? 'disabled' : ''; ?>>
                                                        <option value="">-- Pilih Jam Kerja --</option>
                                                        <?php foreach ($jam_kerja as $jk): ?>
                                                            <option value="<?php echo $jk['id']; ?>" <?php echo ($jam_kerja_id == $jk['id']) ? 'selected' : ''; ?>>
                                                                <?php echo $jk['nama_jam_kerja']; ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </td>
                                                <td>
                                                    <?php if (isset($jam_kerja_bidang[$h]) && $jam_kerja_bidang[$h]['jam_kerja_id']): ?>
                                                        <small>
                                                            <ul class="mb-0 ps-3">
                                                                <li>Awal Masuk: <?php echo $jam_kerja_bidang[$h]['awal_jam_masuk']; ?></li>
                                                                <li>Jam Masuk: <?php echo $jam_kerja_bidang[$h]['jam_masuk']; ?></li>
                                                                <li>Akhir Masuk: <?php echo $jam_kerja_bidang[$h]['akhir_jam_masuk']; ?></li>
                                                                <li>Jam Pulang: <?php echo $jam_kerja_bidang[$h]['jam_pulang']; ?></li>
                                                                <li>Akhir Pulang: <?php echo $jam_kerja_bidang[$h]['akhir_jam_pulang']; ?></li>
                                                            </ul>
                                                        </small>
                                                    <?php else: ?>
                                                        <small class="text-muted">Tidak ada jam kerja yang dipilih</small>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <div class="mt-3">
                                <button type="submit" name="update" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Simpan Perubahan
                                </button>
                                <a href="hari_kerja.php?bidang_id=<?php echo $selected_bidang_id; ?>" class="btn btn-success ms-2">
                                    <i class="fas fa-calendar-day"></i> Atur Hari Kerja
                                </a>
                            </div>
                        </form>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <p>Silakan pilih bidang terlebih dahulu.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Informasi Jam Kerja Bidang</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5 class="alert-heading">Penggunaan Jam Kerja Bidang:</h5>
                        <ul>
                            <li>Jam kerja bidang digunakan untuk mengatur jam kerja yang berbeda untuk setiap hari dalam seminggu.</li>
                            <li>Anda harus mengatur hari kerja terlebih dahulu di menu <a href="hari_kerja.php">Hari Kerja</a>.</li>
                            <li>Anda hanya dapat memilih jam kerja untuk hari yang berstatus "Hari Kerja".</li>
                            <li>Jika hari berstatus "Hari Libur", jam kerja tidak akan berpengaruh.</li>
                        </ul>
                        <hr>
                        <p class="mb-0">Pastikan untuk mengatur jam kerja bidang sesuai dengan kebijakan perusahaan untuk setiap bidang.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../includes/footer.php';
?>
