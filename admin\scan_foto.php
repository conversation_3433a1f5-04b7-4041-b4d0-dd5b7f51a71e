<?php
// Endpoint untuk menscan foto presensi per bulan dan menandai mana yang tidak ada wajah
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Hanya admin
checkAccess('admin');

header('Content-Type: application/json');

// Ambil parameter bulan & tahun
$bulan = isset($_GET['bulan']) ? intval($_GET['bulan']) : intval(date('n'));
$tahun = isset($_GET['tahun']) ? intval($_GET['tahun']) : intval(date('Y'));

if ($bulan < 1 || $bulan > 12) {
    echo json_encode(['status' => 'error', 'message' => 'Parameter bulan tidak valid']);
    exit;
}

// Ambil data presensi pada bulan & tahun tersebut
$bulan_esc = mysqli_real_escape_string($conn, str_pad((string)$bulan, 2, '0', STR_PAD_LEFT));
$tahun_esc = mysqli_real_escape_string($conn, (string)$tahun);

$sql = "SELECT p.id, p.user_id, p.tanggal, p.foto_masuk, p.foto_pulang, u.nama
        FROM presensi p
        JOIN users u ON p.user_id = u.id
        WHERE MONTH(p.tanggal) = '$bulan_esc' AND YEAR(p.tanggal) = '$tahun_esc'
        ORDER BY p.tanggal ASC";

$result = mysqli_query($conn, $sql);

$entries = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        // Bangun absolute path gambar (jika ada)
        $files = [];
        if (!empty($row['foto_masuk'])) {
            $files[] = [
                'type' => 'masuk',
                'filename' => $row['foto_masuk'],
                'url' => BASE_URL . 'uploads/' . $row['foto_masuk']
            ];
        }
        if (!empty($row['foto_pulang'])) {
            $files[] = [
                'type' => 'pulang',
                'filename' => $row['foto_pulang'],
                'url' => BASE_URL . 'uploads/' . $row['foto_pulang']
            ];
        }

        if (!empty($files)) {
            $entries[] = [
                'presensi_id' => $row['id'],
                'user_id' => $row['user_id'],
                'nama' => $row['nama'],
                'tanggal' => $row['tanggal'],
                'files' => $files,
            ];
        }
    }
}

if (empty($entries)) {
    echo json_encode([
        'status' => 'success',
        'message' => 'Tidak ada foto pada periode ini.',
        'data' => [],
        'total_checked' => 0,
        'total_no_face' => 0,
    ]);
    exit;
}

// Kembalikan data foto untuk di-scan di frontend menggunakan face-api.js
echo json_encode([
    'status' => 'success',
    'message' => 'Data foto berhasil diambil untuk di-scan',
    'data' => $entries,
    'total_checked' => count($entries),
]);
exit;


