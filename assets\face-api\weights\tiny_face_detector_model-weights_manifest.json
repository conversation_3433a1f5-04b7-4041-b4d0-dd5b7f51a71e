[{"weights": [{"name": "conv0/filters", "shape": [3, 3, 3, 16], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.009007044399485869, "min": -1.2069439495311063}}, {"name": "conv0/bias", "shape": [16], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.005263455241334205, "min": -0.9211046672334858}}, {"name": "conv1/depthwise_filter", "shape": [3, 3, 16, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.004001977630690033, "min": -0.5042491814669441}}, {"name": "conv1/pointwise_filter", "shape": [1, 1, 16, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.013836609615999109, "min": -1.411334180831909}}, {"name": "conv1/bias", "shape": [32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0015159862590771096, "min": -0.30926119685173037}}, {"name": "conv2/depthwise_filter", "shape": [3, 3, 32, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.002666276225856706, "min": -0.317286870876948}}, {"name": "conv2/pointwise_filter", "shape": [1, 1, 32, 64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.015265831292844286, "min": -1.6792414422128714}}, {"name": "conv2/bias", "shape": [64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0020280554598453, "min": -0.37113414915168985}}, {"name": "conv3/depthwise_filter", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.006100742489683862, "min": -0.8907084034938438}}, {"name": "conv3/pointwise_filter", "shape": [1, 1, 64, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.016276211832083907, "min": -2.0508026908425725}}, {"name": "conv3/bias", "shape": [128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.003394414279975143, "min": -0.7637432129944072}}, {"name": "conv4/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.006716050119961009, "min": -0.8059260143953211}}, {"name": "conv4/pointwise_filter", "shape": [1, 1, 128, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.021875603993733724, "min": -2.8875797271728514}}, {"name": "conv4/bias", "shape": [256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0041141652009066415, "min": -0.8187188749804216}}, {"name": "conv5/depthwise_filter", "shape": [3, 3, 256, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.008423839597141042, "min": -0.9013508368940915}}, {"name": "conv5/pointwise_filter", "shape": [1, 1, 256, 512], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.030007277283014035, "min": -3.8709387695088107}}, {"name": "conv5/bias", "shape": [512], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.008402082966823203, "min": -1.4871686851277068}}, {"name": "conv8/filters", "shape": [1, 1, 512, 25], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.028336129469030042, "min": -4.675461362389957}}, {"name": "conv8/bias", "shape": [25], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.002268134028303857, "min": -0.41053225912299807}}], "paths": ["tiny_face_detector_model-shard1"]}]